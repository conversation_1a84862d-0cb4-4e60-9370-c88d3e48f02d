﻿<div class="layuimini-container layuimini-page-anim">
    <div class="layuimini-main">
        <fieldset class="table-search-fieldset">
            <legend>搜索信息</legend>
            <div style="margin: 10px 10px 10px 10px">
                <form class="layui-form layui-form-pane" action="">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">RFQ编码</label>
                            <div class="layui-input-inline">
                                <input type="text" name="RFQNo" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">客户编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="CustomerCode" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">客户名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="Customer" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">项目名</label>
                            <div class="layui-input-inline">
                                <input type="text" name="ProjectName" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button type="submit" class="layui-btn layui-btn-primary" lay-submit lay-filter="data-search-btn"><i class="layui-icon"></i> 搜 索</button>
                        </div>
                    </div>
                </form>
            </div>
        </fieldset>

        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-normal layui-btn-sm data-add-btn" lay-event="add"> 添加 </button>
                <button class="layui-btn layui-btn-sm layui-btn-danger data-delete-btn" lay-event="delete"> 删除 </button>
            </div>
        </script>

        <table class="layui-hide" id="currentTableId" lay-filter="currentTableFilter"></table>

        <script type="text/html" id="currentTableBar">
            <a class="layui-btn layui-btn-normal layui-btn-xs data-count-edit" lay-event="edit">编辑</a>
            <a class="layui-btn layui-btn-xs layui-btn-danger data-count-delete" lay-event="delete">删除</a>
        </script>

    </div>
</div>
<script>

    layui.use(['form', 'table', 'miniPage', 'element'], function () {
        var $ = layui.jquery,
            form = layui.form,
            table = layui.table,
            miniPage = layui.miniPage;

        table.render({
            elem: '#currentTableId',
            url: 'ashx/ProjectController.ashx?action=Query',
            toolbar: '#toolbarDemo',
            method:'post',
            defaultToolbar: ['filter', 'exports', 'print', {
                title: '提示',
                layEvent: 'LAYTABLE_TIPS',
                icon: 'layui-icon-tips'
            }],
            cols: [[
                { type: "checkbox", width: 50 },
                { field: 'RFQNo', width: 150, title: 'RFQ编码', sort: true },
                { field: 'CustomerCode', width: 100, title: '客户编号' },
                { field: 'RFQCustomerCode', width: 120, title: 'RFQ客户编码' },
                { field: 'Customer', width: 100, title: '终端客户'},
                { field: 'ProjectApp', width: 100, title: '项目应用' },
                { field: 'ProjectName', width:100,title: '项目名称' },
                { field: 'DesignLoc', width: 100, title: '设计地' },
                { field: 'EAU', width: 80, title: 'EAU', sort: true },
                { field: 'CostReduYear', width: 100, title: '年成本下降比例要求', templet: function(d) { return d.CostReduYear ? d.CostReduYear + '%' : ''; } },
                { field: 'SuppCur', width: 100, title: '报价币别', sort: true },
                { title: '操作', minWidth: 150, toolbar: '#currentTableBar', align: "center" }
            ]],
            limits: [10, 15, 20, 25, 50, 100],
            limit: 10,
            page: true,
            skin: 'line',
            
        });

        // 监听搜索操作
        form.on('submit(data-search-btn)', function (data) {
            var result =  data.field ;
            //console.log(result.Customer);
            //layer.alert(result, {
            //    title: '最终的搜索信息'
            //});

            //执行搜索重载
            table.reload('currentTableId', {
                page: {
                    curr: 1
                }
                , where: result,
                error: {

                }
                
            }, 'data');

            return false;
        });

        /**
         * toolbar事件监听
         */
        table.on('toolbar(currentTableFilter)', function (obj) {
            if (obj.event === 'add') {   // 监听添加操作
                var content = miniPage.getHrefContent('Flow/EditProject.html');
                var openWH = miniPage.getOpenWidthHeight();

                var index = layer.open({
                    title: '添加用户',
                    type: 1,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: [openWH[0] + 'px', openWH[1] + 'px'],
                    offset: [openWH[2] + 'px', openWH[3] + 'px'],
                    content: content,
                    success: function (layero, index) {
                        //console.log(data);
                        form.val("editform");
                    },
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
            } else if (obj.event === 'delete') {  // 监听删除操作
                var checkStatus = table.checkStatus('currentTableId')
                    , data = checkStatus.data;
                console.log(data);
                
                if (data.length == 0) {
                    layer.msg('请选择要删除的项', { icon: 0 });
                    return;
                }
                var nos = data.map(function (item) {
                    return item.RFQNo;
                }).join(',');
                $.ajax({
                    url: 'ashx/ProjectController.ashx?action=BatchDelete',
                    type: 'POST',
                    data: { nos: nos },
                    success: function (response) {
                        var res = JSON.parse(response);
                        console.log(res);
                        if (res.result === 'success') {
                            layer.msg('删除成功', { icon: 1 });
                            // 重新加载表格数据
                            table.reload('currentTableId');
                        } else {
                            layer.msg('删除失败: ' + res.msg, { icon: 2 });
                        }
                    },
                    error: function () {
                        layer.msg('删除请求失败', { icon: 2 });
                    }
                });
                //layer.alert(JSON.stringify(data));
            }
        });

        //监听表格复选框选择
        table.on('checkbox(currentTableFilter)', function (obj) {
            //console.log(obj)
        });

        table.on('tool(currentTableFilter)', function (obj) {
            var data = obj.data;
            if (obj.event === 'edit') {

                var content = miniPage.getHrefContent('Flow/EditProject.html');
                var openWH = miniPage.getOpenWidthHeight();

                var index = layer.open({
                    title: '编辑用户',
                    type: 1,
                    shade: 0.2,
                    maxmin: true,
                    shadeClose: true,
                    area: [openWH[0] + 'px', openWH[1] + 'px'],
                    offset: [openWH[2] + 'px', openWH[3] + 'px'],
                    content: content,
                    success: function (layero, index) {
                        //console.log(data);
                        form.val("editform", data);
                        //监听提交
                        form.on('submit(saveBtn)', function (data) {
                            var field = data.field;
                            console.log(field);
                            $.ajax({
                                url: 'ashx/ProjectController.ashx?action=Update',
                                type: 'post',
                                data: field,
                                success: function (data) {
                                    var objdata = eval("(" + data + ")")
                                    if (objdata.result=="success") {
                                        //console.log(field);
                                        layer.msg(objdata.msg, { icon: 1, time: 2000 }, function () {
                                            layer.close(index);
                                            table.reload('currentTableId')
                                        })
                                    }
                                    else {
                                        layer.alert(objdata.msg);
                                    }

                                },
                                error: function (data) {
                                    layer.alert(objdata.msg);
                                }
                            });
                            return false;

                        });
                    },
                });
                $(window).on("resize", function () {
                    layer.full(index);
                });
                return false;
            } else if (obj.event === 'delete') {
                layer.confirm('真的删除行么', function (index) {
                    var nos = data.RFQNo;
                    $.ajax({
                        url: 'ashx/ProjectController.ashx?action=BatchDelete',
                        type: 'POST',
                        data: { nos: nos },
                        success: function (response) {
                            var res = JSON.parse(response);
                            console.log(res);
                            if (res.result === 'success') {
                                layer.msg('删除成功', { icon: 1 });
                                // 重新加载表格数据
                                table.reload('currentTableId');
                            } else {
                                layer.msg('删除失败: ' + res.msg, { icon: 2 });
                            }
                        },
                        error: function () {
                            layer.msg('删除请求失败', { icon: 2 });
                        }
                    });
                    layer.close(index);
                });
            }
        });

    });</script>