﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RFQ.Dao.Model;
using System.Data.Entity.ModelConfiguration;

namespace RFQ.Dao.Mapping
{
    public class ProjectMap: EntityTypeConfiguration<ProjectInfo>
    {
        public ProjectMap()
        {
            ToTable("RFQ_PROJECT_INFO", "dbo");

            HasKey(p => p.Id);
            HasIndex(p => p.RFQNo);

            Property(p => p.Id)
                .HasColumnName("ID")
                .HasMaxLength(50)
                .IsRequired();

            Property(p => p.RFQNo)
                .HasColumnName("RFQ_NO")
                .HasMaxLength(50)
                .IsRequired();

            Property(p => p.CustomerCode)
                .HasColumnName("CUSTOMER_CODE")
                .HasMaxLength(255)
                .IsOptional();

            Property(p => p.RFQCustomerCode)
                .HasColumnName("RFQ_CUSTOMER_CODE")
                .HasMaxLength(255)
                .IsOptional();

            Property(p => p.Customer)
                .HasColumnName("CUSTOMER")
                .HasMaxLength(255)
                .IsOptional();

            Property(p => p.EndCustomer)
                .HasColumnName("END_CUSTOMER")
                .HasMaxLength(255)
                .IsOptional();

            Property(p => p.ProjectApp)
                .HasColumnName("PROJECT_APP")
                .HasMaxLength(255)
                .IsOptional();


            Property(p => p.DesignLoc)
                .HasColumnName("DESIGN_LOC")
                .HasMaxLength(50)
                .IsOptional();

            

            Property(p => p.CostReduYear)
                .HasColumnName("COST_REDU_YEAR")
                .HasMaxLength(50)
                .IsOptional();

            Property(p => p.SuppCur)
                .HasColumnName("SUPP_CURR")
                .HasMaxLength(50)
                .IsOptional();

            Property(p => p.Rate)
                .HasColumnName("RATE")
                .HasPrecision(18, 2)
                .IsOptional();

            Property(p => p.Status)
                .HasColumnName("STATUS")
                .HasMaxLength(50)
                .IsOptional();

            Property(p => p.CreateTime)
                .HasColumnName("CREATETIME")
                .IsOptional();

            Property(p => p.CreateUser)
                .HasColumnName("CREATEUSER")
                .HasMaxLength(50)
                .IsOptional();

            Property(p => p.CloseTime)
                .HasColumnName("CLOSETIME")
                .IsOptional();
        }
    }
}
