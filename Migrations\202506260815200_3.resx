﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>