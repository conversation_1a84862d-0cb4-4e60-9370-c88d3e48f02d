﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RFQ.Dao.Model
{
    public partial class ProjectInfo:Entity
    {
        /// <summary>
	    /// RFQ流程编号
	    /// </summary>
        public string RFQNo { get; set; }
        
        
        /// <summary>
	    /// 客户编号
	    /// </summary>
        public string CustomerCode { get; set; }

        /// <summary>
	    /// RFQ客户编码
	    /// </summary>
        public string RFQCustomerCode { get; set; }

        /// <summary>
	    /// 终端客户
	    /// </summary>
        public string Customer { get; set; }

        public string EndCustomer { get; set; }

        /// <summary>
        /// 项目应用
        /// </summary>
        public string ProjectApp { get; set; }

        

        /// <summary>
        /// 设计地
        /// </summary>
        public string DesignLoc { get; set; }

        

        /// <summary>
        /// 年成本比例下降要求
        /// </summary>
        public string CostReduYear { get; set; }

        /// <summary>
        /// 报价币别
        /// </summary>
        public string SuppCur { get; set; }

        /// <summary>
        /// 报价汇率
        /// </summary>
        public decimal? Rate { get; set; }

        public string Status { get; set; }

        public DateTime CreateTime { get; set; }

        public string CreateUser { get; set; }

        public DateTime? CloseTime { get; set; }

    }
}
