﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C6564944-359E-4928-BB3C-158A4F4674C1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RFQ.Dao</RootNamespace>
    <AssemblyName>RFQ.Dao</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\WebApplication1\packages\EntityFramework.6.5.1\lib\net40\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.Extended, Version=6.0.0.0, Culture=neutral, PublicKeyToken=05b7e29bdd433584, processorArchitecture=MSIL">
      <HintPath>..\WebApplication1\packages\EntityFramework.Extended.6.1.0.168\lib\net40\EntityFramework.Extended.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\WebApplication1\packages\EntityFramework.6.5.1\lib\net40\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\WebApplication1\packages\Newtonsoft.Json.13.0.3\lib\net40\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\WebApplication1\packages\Oracle.OracleClient.4.121.2\lib\net40\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseRepository.cs" />
    <Compile Include="BaseApp.cs" />
    <Compile Include="ConvertToList.cs" />
    <Compile Include="Infrastructure\BomCombineModel.cs" />
    <Compile Include="Infrastructure\DBHelper.cs" />
    <Compile Include="Infrastructure\OracleDBHelper.cs" />
    <Compile Include="Mapping\BomGroupMap.cs" />
    <Compile Include="Mapping\BomInfoMap.cs" />
    <Compile Include="Mapping\BomQtyInfoMap.cs" />
    <Compile Include="Mapping\CsvConfigMap.cs" />
    <Compile Include="Mapping\CsvFieldMap.cs" />
    <Compile Include="Mapping\FlowDetailMap.cs" />
    <Compile Include="Mapping\FlowFormMap.cs" />
    <Compile Include="Mapping\MatchTipDataMap.cs" />
    <Compile Include="Mapping\MaterialGroupMap.cs" />
    <Compile Include="Mapping\ModuelMap.cs" />
    <Compile Include="Mapping\ProjectMap.cs" />
    <Compile Include="Mapping\QuoRecordMap.cs" />
    <Compile Include="Mapping\QuoStatusMap.cs" />
    <Compile Include="Mapping\RateMap.cs" />
    <Compile Include="Mapping\RFQTemplateMap.cs" />
    <Compile Include="Mapping\RoleAuthMap.cs" />
    <Compile Include="Mapping\SupplierGroupMap.cs" />
    <Compile Include="Mapping\SupplierInfoMap.cs" />
    <Compile Include="Mapping\TypeInfoMap.cs" />
    <Compile Include="Mapping\UserRoleMap.cs" />
    <Compile Include="Migrations\202505270827270_InitialCreate.cs" />
    <Compile Include="Migrations\202505270827270_InitialCreate.designer.cs">
      <DependentUpon>202505270827270_InitialCreate.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\202506260815200_3.cs" />
    <Compile Include="Migrations\202506260815200_3.designer.cs">
      <DependentUpon>202506260815200_3.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Mapping\UserMap.cs" />
    <Compile Include="Model\BomGroup.cs" />
    <Compile Include="Model\BomInfo.cs" />
    <Compile Include="Model\BomQtyInfo.cs" />
    <Compile Include="Model\CsvConfig.cs" />
    <Compile Include="Model\CsvField.cs" />
    <Compile Include="Model\FlowDetail.cs" />
    <Compile Include="Model\FlowForm.cs" />
    <Compile Include="Model\MatchTipData.cs" />
    <Compile Include="Model\MaterialGroup.cs" />
    <Compile Include="Model\Module.cs" />
    <Compile Include="Model\ProjectInfo.cs" />
    <Compile Include="Model\QuoRecord.cs" />
    <Compile Include="Model\QuoStatus.cs" />
    <Compile Include="Model\Rate.cs" />
    <Compile Include="Model\RFQTemplate.cs" />
    <Compile Include="Model\RoleAuth.cs" />
    <Compile Include="Model\SupplierGroup.cs" />
    <Compile Include="Model\SupplierInfo.cs" />
    <Compile Include="Model\TypeInfo.cs" />
    <Compile Include="Model\UserRole.cs" />
    <Compile Include="UnitWork.cs" />
    <Compile Include="Entity.cs" />
    <Compile Include="Infrastructure\JsonHelper.cs" />
    <Compile Include="Infrastructure\DynamicLinq.cs" />
    <Compile Include="Infrastructure\DynamicQueryable.cs" />
    <Compile Include="Infrastructure\Filter.cs" />
    <Compile Include="Infrastructure\Response.cs" />
    <Compile Include="InterFace\IRepository.cs" />
    <Compile Include="InterFace\IUnitWork.cs" />
    <Compile Include="Model\User.cs" />
    <Compile Include="RFQDBContext.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Migrations\202505270827270_InitialCreate.resx">
      <DependentUpon>202505270827270_InitialCreate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202506260815200_3.resx">
      <DependentUpon>202506260815200_3.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\WebApplication1\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>