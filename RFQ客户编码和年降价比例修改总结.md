# RFQ客户编码和年降价比例修改总结

## 修改概述
本次修改主要包含三个部分：
1. 在基本信息中添加RFQ客户编码栏位
2. 给达成的年降价比例添加%符号显示
3. 修复下载模板404问题

## 详细修改内容

### 1. 前端界面修改

#### Flow/CreateRfq.aspx
- **添加RFQ客户编码输入框**：在客户编号和客户名之间添加了RFQ客户编码输入字段
- **年降价比例添加%符号**：使用CSS样式在输入框右侧添加%符号显示
- **修复下载模板链接**：修改下载链接路径为`../批量上传.xlsx`并添加download属性
- **更新JavaScript逻辑**：
  - 在数据加载时包含RFQCustomerCode字段
  - 在表单提交时传递rfqcustomercode参数
  - 在上传成功后赋值RFQCustomerCode字段

#### Flow/CreateRfq.html
- **添加表格列**：在表格中添加RFQ客户编码列显示
- **年降价比例显示优化**：使用templet函数在年降价比例后自动添加%符号

### 2. 后端模型修改

#### Model/ProjectInfo.cs
- **添加属性**：新增`RFQCustomerCode`属性，用于存储RFQ客户编码
- **添加注释**：为新属性添加中文注释说明

#### Mapping/ProjectMap.cs
- **添加字段映射**：为RFQCustomerCode属性添加数据库字段映射
- **字段配置**：映射到RFQ_CUSTOMER_CODE列，最大长度255，可选字段

### 3. 后端控制器修改

#### ashx/ProjectController.ashx.cs
- **添加参数处理**：在Add方法中添加对rfqcustomercode参数的处理
- **数据赋值**：将前端传递的RFQ客户编码值赋给ProjectInfo对象

### 4. 数据库迁移

#### Migrations/AddRFQCustomerCodeField.cs
- **创建迁移文件**：添加RFQ_CUSTOMER_CODE字段到RFQ_PROJECT_INFO表
- **字段类型**：String类型，最大长度255

## 功能说明

### RFQ客户编码字段
- **位置**：位于基本信息表单中，客户编号和客户名之间
- **类型**：文本输入框，非必填字段
- **存储**：存储在RFQ_PROJECT_INFO表的RFQ_CUSTOMER_CODE字段中
- **显示**：在RFQ列表页面中作为独立列显示

### 年降价比例%符号
- **输入界面**：在输入框右侧显示%符号，提示用户输入格式
- **列表显示**：在表格中自动在数值后添加%符号显示
- **样式实现**：使用CSS绝对定位在输入框内右侧显示%符号

### 下载模板修复
- **路径修正**：修改为正确的相对路径`../批量上传.xlsx`
- **下载属性**：添加download属性，指定下载文件名为"批量上传模板.xlsx"
- **兼容性**：确保在不同浏览器中都能正常下载

## 注意事项

1. **数据库更新**：需要运行数据库迁移来添加RFQ_CUSTOMER_CODE字段
2. **向后兼容**：RFQ客户编码字段为可选字段，不影响现有数据
3. **前端验证**：年降价比例字段保持原有的数字验证逻辑
4. **文件路径**：确保批量上传.xlsx文件存在于项目根目录

## 测试建议

1. **功能测试**：
   - 测试RFQ客户编码字段的输入和保存
   - 验证年降价比例%符号显示正常
   - 测试下载模板功能是否正常工作

2. **兼容性测试**：
   - 测试现有RFQ记录的显示和编辑
   - 验证新旧数据的兼容性

3. **界面测试**：
   - 检查表单布局是否正常
   - 验证表格列显示是否合适
