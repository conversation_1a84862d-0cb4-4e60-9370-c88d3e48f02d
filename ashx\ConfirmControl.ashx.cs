﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RFQ.Dao;
using RFQ.Dao.Infrastructure;
using RFQ.Dao.Model;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Dynamic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.SessionState;

namespace WebApplication1.ashx
{
    /// <summary>
    /// ConfirmControl 的摘要说明
    /// </summary>
    public class ConfirmControl : IHttpHandler, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            string action = context.Request.Params["action"].ToString();
            switch (action)
            {
                case "QueryBom":
                    QueryBom(context);
                    break;
                case "QueryColumn":
                    QueryColumn(context);
                    break;
                case "UpdateStatus":
                    UpdateStatus(context);
                    break;
                case "GetMatchOptions":
                    GetMatchOptions(context);
                    break;
                case "GetMatchDetails":
                    GetMatchDetails(context);
                    break;
                case "GetMaterialGroups":
                    GetMaterialGroups(context);
                    break;
                default:
                    break;
            }
        }

        

        public void QueryColumn(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                RFQDBContext _context = new RFQDBContext();
                string no = context.Request.QueryString["RFQNo"] ?? (string)HttpContext.Current.Session["RFQNo"];
                //string no = "RFQ-IBS-20240910-001";
                var config = new BaseRepository<RFQTemplate>().FindSingle(u => u.RFQNO == no);
                //string code = HttpContext.Current.Session["CustomerCode"].ToString();
                //string code = "IBS-AH";
                var customercode = config.CUSTOMERCODE;
                var templatecode = config.TEMPLATECODE;
                var result = _context.CsvFields.Where(r => r.CustomerCode == customercode && r.TemplateCode == templatecode)
                .Where(r => !_context.TypeInfos
                .Where(t => t.RFQ_NO == no)
                .Select(t => t.MODEL)
                .Contains(r.DatabaseField))
                .ToList();

                var models = new BaseRepository<TypeInfo>().ExecuteRawSql("select * from RFQ_TYPE_INFO where RFQ_NO=@no order by case when BOMQTYFIELD is null then 99 else CAST(SUBSTRING(BOMQTYFIELD,4,2) AS INT) end asc,CREATE_TIME asc", new SqlParameter("@no", no)).Select(c => c.MODEL);
                d.result = "success";
                d.col = result;
                d.models = models;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void QueryBom(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {

                string no = context.Request.QueryString["RFQNo"] ?? (string)HttpContext.Current.Session["RFQNo"];
                //string no = "RFQ-IBS-20240910-001";
                var config = new BaseRepository<RFQTemplate>().FindSingle(u => u.RFQNO == no);
                var customercode = config.CUSTOMERCODE;
                var templatecode = config.TEMPLATECODE;
                
                MatchTiptop(no);
                CreatePartNumber(no);


                IQueryable<BomInfo> result = Enumerable.Empty<BomInfo>().AsQueryable();
                var res = new DbHelper().GetRFQData(no, customercode, templatecode);
                var aa = new BaseRepository<TypeInfo>().Find(c => c.RFQ_NO == no).ToList();
                context.Response.Write(JsonConvert.SerializeObject(res));
            }
            catch (Exception ex)
            {
                
                
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void GetMatchOptions(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var id = context.Request.Params["id"];
                var partlist = new BaseRepository<MatchTipData>().Find(c => c.BOM_ID == id);
                context.Response.Write(JsonConvert.SerializeObject(partlist));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }


        public void GetMatchDetails(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var id = context.Request.Params["id"];
                var partnum = context.Request.Params["partnumber"];
                var partdetail = new BaseRepository<MatchTipData>().FindSingle(c => c.BOM_ID == id&&c.MATCH_PARTNUMBER==partnum);
                context.Response.Write(JsonConvert.SerializeObject(partdetail));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        /// <summary>
        /// 获取物料组数据
        /// </summary>
        /// <param name="context"></param>
        public void GetMaterialGroups(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var result = new BaseRepository<BomGroup>().Find(null);
                var materialGroups = result.Select(g => new {
                    Id = g.Id,
                    GroupName = g.GroupName
                }).ToList();

                d.code = 0;
                d.msg = "获取物料组数据成功";
                d.data = materialGroups;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        //从tiptop模糊匹配和完全匹配数据
        public void MatchTiptop(string no) 
        {
            //var no = (string)HttpContext.Current.Session["RFQNo"];
            //string no = "RFQ-IBS-20240910-001";
            OracleHelper oraclehelper = new OracleHelper();
            var bomdata = new BaseRepository<BomInfo>().Find(u => u.RFQNO == no);
            foreach (var b in bomdata)
            {

                string sql1 = "Select distinct ima01,ima021,bmj02,bmj04 from ima_file left join bmj_file on ima01=bmj01 where ima021 = '" + b.CUST_PARTNUMBER + "' ";

                string sql2 = "select distinct ima01,ima021,bmj02,bmj04 from ima_file,bmj_file where ima01=bmj01 and bmj04 = '" + b.MAN_PARTNUMBER + "' ";

                string sql3 = "select distinct ima01,ima021,bmj02,bmj04 from ima_file,bmj_file where ima01=bmj01 and bmj04 like '%" + b.MAN_PARTNUMBER + "%' ";


                DataTable dt = oraclehelper.ExecuteQuery(b.PRIORITY == "customer" ? sql1 : sql3);
                var checkid = new BaseRepository<MatchTipData>().FindSingle(u => u.BOM_ID == b.Id);
                if (checkid != null)
                {
                    new BaseRepository<MatchTipData>().Delete(u => u.BOM_ID == b.Id);
                    
                }
                if (dt.Rows.Count > 0&&!String.IsNullOrEmpty(b.MAN_PARTNUMBER))
                {
                    
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        string partnumber = dt.Rows[i]["ima01"].ToString();
                        string cust = dt.Rows[i]["ima021"].ToString();
                        string MANUFACTURER = dt.Rows[i]["bmj02"].ToString();
                        string MPN = dt.Rows[i]["bmj04"].ToString();
                        MatchTipData md = new MatchTipData();

                        md.BOM_ID = b.Id;
                        md.MATCH_PARTNUMBER = partnumber;
                        md.MATCH_CUST = cust;
                        md.MATCH_MANUFACTURER = MANUFACTURER;
                        md.MATCH_MPN = MPN;
                        md.COLOR_PARTNUMBER = partnumber == "" ? "0" : "1";
                        md.COLOR_CUST = cust == "" ? "0" : "1";
                        md.COLOR_MANUFACTURER = MANUFACTURER == "" ? "0" : "1";
                        md.COLOR_MPN = MPN == "" ? "0" : "1";
                        md.REQUIRE_QUOTE = 0;
                        
                        
                        new BaseRepository<MatchTipData>().Add(md);
                        
                    }
                    //if (b.PRIORITY == "mpn")
                    //{
                        //DataTable dt2 = oraclehelper.ExecuteQuery(sql3);
                        //if(dt2.Rows.Count>0)
                        //{
                            //var data = new BaseRepository<MatchTipData>().FindSingle(u => u.Id == b.Id);
                            //new BaseRepository<MatchTipData>().Update(u => u.Id == b.Id, u => new MatchTipData
                            //{
                            //    MATCH_PARTNUMBER = data.COLOR_PARTNUMBER == "0" ? dt2.Rows[0]["ima01"].ToString() : data.MATCH_PARTNUMBER,
                            //    MATCH_CUST = data.COLOR_CUST == "0" ? dt2.Rows[0]["ima021"].ToString() : data.MATCH_CUST,
                            //    MATCH_MANUFACTURER = data.COLOR_MANUFACTURER == "0" ? dt2.Rows[0]["bmj02"].ToString() : data.MATCH_MANUFACTURER,
                            //    MATCH_MPN = data.COLOR_MPN == "0" ? dt2.Rows[0]["bmj04"].ToString() : data.MATCH_MPN,
                            //});
                            //new BaseRepository<BomInfo>().Update(u => u.Id == b.Id, u => new BomInfo
                            //{
                            //    MATCH_PARTNUMBER = data.COLOR_PARTNUMBER=="0"? dt2.Rows[0]["ima01"].ToString():data.MATCH_PARTNUMBER,
                            //    MATCH_CUST = data.COLOR_CUST=="0"? dt2.Rows[0]["ima021"].ToString(): data.MATCH_CUST,
                            //    MATCH_MANUFACTURER = data.COLOR_MANUFACTURER=="0"? dt2.Rows[0]["bmj02"].ToString(): data.MATCH_MANUFACTURER,
                            //    MATCH_MPN = data.COLOR_MPN=="0"? dt2.Rows[0]["bmj04"].ToString(): data.MATCH_MPN,

                            //});
                            //data = new BaseRepository<MatchTipData>().FindSingle(u => u.Id == b.Id);
                            //new BaseRepository<MatchTipData>().Update(u => u.Id == b.Id, u => new MatchTipData
                            //{
                            //    COLOR_PARTNUMBER = !String.IsNullOrEmpty(data.MATCH_PARTNUMBER) && data.COLOR_PARTNUMBER == "0" ? "2" : data.COLOR_PARTNUMBER,
                            //    COLOR_CUST = !String.IsNullOrEmpty(data.MATCH_CUST) && data.COLOR_CUST == "0" ? "2" : data.COLOR_CUST,
                            //    COLOR_MANUFACTURER = !String.IsNullOrEmpty(data.MATCH_MANUFACTURER) && data.COLOR_MANUFACTURER == "0" ? "2" : data.COLOR_MANUFACTURER,
                            //    COLOR_MPN = !String.IsNullOrEmpty(data.MATCH_MPN) && data.COLOR_MPN == "0" ? "2" : data.COLOR_MPN,
                            //});
                            //new BaseRepository<BomInfo>().Update(u => u.Id == b.Id, u => new BomInfo
                            //{
                            //    COLOR_PARTNUMBER = !String.IsNullOrEmpty(data.MATCH_PARTNUMBER)&& data.COLOR_PARTNUMBER=="0" ? "2" : data.COLOR_PARTNUMBER,
                            //    COLOR_CUST = !String.IsNullOrEmpty(data.MATCH_CUST) && data.COLOR_CUST == "0" ? "2" : data.COLOR_CUST,
                            //    COLOR_MANUFACTURER = !String.IsNullOrEmpty(data.MATCH_MANUFACTURER) && data.COLOR_MANUFACTURER == "0" ? "2" : data.COLOR_MANUFACTURER,
                            //    COLOR_MPN = !String.IsNullOrEmpty(data.MATCH_MPN) && data.COLOR_MPN == "0" ? "2" : data.COLOR_MPN,
                            //});
                        //}
                    //}
                }
                else
                {
                    MatchTipData md = new MatchTipData();
                    md.BOM_ID = b.Id;
                    md.REQUIRE_QUOTE = 0;
                    new BaseRepository<MatchTipData>().Add(md);
                }
            }
        }

        //没有匹配到hana料号的自己生成
        public void CreatePartNumber(string no)
        {
            try
            {
                string month = DateTime.Now.ToString("yyyyMM");
                using (var context = new RFQDBContext()) // 替换为您的DbContext
                {
                    var result = context.MatchTipDatas
                        .Where(t => context.BomInfos
                            .Where(b => b.RFQNO == no)
                            .Select(b => b.Id)
                            .Contains(t.BOM_ID) &&
                            t.MATCH_PARTNUMBER == null)
                        .ToList();
                    foreach (var bom in result)
                    {
                        var latestnum = new BaseRepository<MatchTipData>().Find(u => u.MATCH_PARTNUMBER.Contains("HANA" + month)).OrderByDescending(u => u.MATCH_PARTNUMBER.Substring(u.MATCH_PARTNUMBER.Length - 7)).FirstOrDefault();
                        string partnum = GetPartNumber(latestnum, no);
                        new BaseRepository<MatchTipData>().Update(u => u.Id == bom.Id, u => new MatchTipData
                        {
                            MATCH_PARTNUMBER = partnum,
                        });

                    }
                }
                
                
            }
            catch(Exception ex)
            {
                throw ex;
            }
        }

        public void UpdateStatus(HttpContext context)
        {

            dynamic d = new ExpandoObject();
            try
            {
                JArray jArray = (JArray)JObject.Parse(context.Request.Params["updateData"])["updateData"];

                foreach (JObject item in jArray)
                {
                    string id = item["Id"].ToString();
                    string matchPartNumber = item["MATCH_PARTNUMBER"].ToString();
                    string matchCust = item["MATCH_CUST"]?.ToString();
                    string matchMan = item["MATCH_MANUFACTURER"]?.ToString();
                    string matchMPN = item["MATCH_MPN"]?.ToString();
                    string materialGroup = item["MATERIAL_GROUP"].ToString();

                    var repository = new BaseRepository<MatchTipData>();

                    // 创建基础条件
                    Expression<Func<MatchTipData, bool>> basePredicate =
                        u => u.BOM_ID == id && u.MATCH_PARTNUMBER == matchPartNumber;

                    // 使用 CombineExpressions 方法来组合条件
                    if (!string.IsNullOrWhiteSpace(matchCust))
                    {
                        Expression<Func<MatchTipData, bool>> custPredicate = u => u.MATCH_CUST == matchCust;
                        basePredicate = CombineExpressions(basePredicate, custPredicate);
                    }

                    if (!string.IsNullOrWhiteSpace(matchMan))
                    {
                        Expression<Func<MatchTipData, bool>> manPredicate = u => u.MATCH_MANUFACTURER == matchMan;
                        basePredicate = CombineExpressions(basePredicate, manPredicate);
                    }

                    if (!string.IsNullOrWhiteSpace(matchMPN))
                    {
                        Expression<Func<MatchTipData, bool>> mpnPredicate = u => u.MATCH_MPN == matchMPN;
                        basePredicate = CombineExpressions(basePredicate, mpnPredicate);
                    }

                    // 更新匹配到的记录
                    repository.Update(basePredicate, u => new MatchTipData
                    {
                        REQUIRE_QUOTE = 1,
                    });

                    new BaseRepository<BomInfo>().Update(u => u.Id == id, u => new BomInfo
                    {
                        MATERIAL_GROUP=materialGroup,
                    });
                }



                //var no = (string)HttpContext.Current.Session["RFQNo"];
                string no = context.Request.QueryString["RFQNo"] ?? (string)HttpContext.Current.Session["RFQNo"];
                new BaseRepository<ProjectInfo>().Update(u => u.RFQNo == no, u => new ProjectInfo
                {
                    
                    Status = "3", //更新状态标识 共用料确认结束 进入发送报价

                });
                d.result = "success";
                d.msg = "更新成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                context.Response.Write(d);
            }
        }

        public string GetPartNumber(MatchTipData partnumber, string suppno)
        {
            if (partnumber != null&&!String.IsNullOrEmpty(partnumber.MATCH_PARTNUMBER))
            {
                string mp = partnumber.MATCH_PARTNUMBER;
                string headDate = mp.Substring(4, 6);
                int lastNumber = int.Parse(mp.Substring(mp.Length - 7));
                //如果数据库最大值流水号中日期和生成日期在同一天，则顺序号加1
                if (headDate == DateTime.Now.ToString("yyyyMM"))
                {
                    lastNumber++;
                    return "HANA" + headDate + lastNumber.ToString("0000000");
                }
            }
            return "HANA" + DateTime.Now.ToString("yyyyMM") + "0000001";
        }

        // 表达式组合帮助方法
        private static Expression<Func<T, bool>> CombineExpressions<T>(
            Expression<Func<T, bool>> expr1,
            Expression<Func<T, bool>> expr2)
        {
            // 创建参数映射
            var parameter = Expression.Parameter(typeof(T));

            // 创建访问者来替换参数
            var visitor = new ParameterReplacer(expr1.Parameters[0], parameter);
            var left = visitor.Visit(expr1.Body);

            visitor = new ParameterReplacer(expr2.Parameters[0], parameter);
            var right = visitor.Visit(expr2.Body);

            // 组合表达式
            var combined = Expression.AndAlso(left, right);

            // 返回新的Lambda表达式
            return Expression.Lambda<Func<T, bool>>(combined, parameter);
        }

        // 参数替换访问者类
        public class ParameterReplacer : ExpressionVisitor
        {
            private readonly ParameterExpression _oldParameter;
            private readonly ParameterExpression _newParameter;

            public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
            {
                _oldParameter = oldParameter;
                _newParameter = newParameter;
            }

            protected override Expression VisitParameter(ParameterExpression node)
            {
                return node == _oldParameter ? _newParameter : base.VisitParameter(node);
            }
        }



        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}