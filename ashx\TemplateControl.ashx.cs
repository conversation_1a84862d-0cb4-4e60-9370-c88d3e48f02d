﻿using Newtonsoft.Json;
using RFQ.Dao;
using RFQ.Dao.Model;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using Aspose.Cells;
using System.IO;
using System.Web.SessionState;
using WebApplication1.tool;
using OfficeOpenXml;

namespace WebApplication1.ashx
{
    /// <summary>
    /// TemplateControl 的摘要说明
    /// </summary>
    public class TemplateControl : IHttpHandler, IRequiresSessionState
    {

        public void ProcessRequest(HttpContext context)
        {
            string action = context.Request.Params["action"].ToString();
            switch (action)
            {
                case "QueryConfig":
                    QueryConfig(context);
                    break;
                case "QuerySingleConfig":
                    QuerySingleConfig(context);
                    break;
                case "Add":
                    Add(context);
                    break;
                case "Update":
                    Update(context);
                    break;
                case "BatchDelete":
                    BatchDelete(context);
                    break;
                case "AddRule":
                    AddRule(context);
                    break;
                case "QueryRule":
                    QueryRule(context);
                    break;
                case "QueryAllRule":
                    QueryAllRule(context);
                    break;
                case "DeleteRule":
                    DeleteRule(context);
                    break;
                case "UploadHeader":
                    UploadHeader(context);
                    break;
                case "UpdateField":
                    UpdateField(context);
                    break;
                case "SaveModelMapping":
                    SaveModelMapping(context);
                    break;
                default:
                    break;
            }
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }

        public void QueryConfig(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var customercode = context.Request.Params["CustomerCode"];
                var result = new BaseRepository<CsvConfig>().Find(null);
                if (!String.IsNullOrEmpty(customercode))
                    result = result.Where(u => u.CustomerCode.Contains(customercode));
                
                d.count = result.Count();
                result = result.OrderBy(u => u.CustomerCode).Skip(int.Parse(context.Request["limit"]) * (int.Parse(context.Request["page"]) - 1)).Take(int.Parse(context.Request["limit"]));
                var datalist = result.ToList();
                foreach (var r in datalist)
                {
                    r.CustomerCode = r.CustomerCode + "-" + r.TemplateCode;
                }
                d.code = 0;
                d.msg = "";
                d.data = datalist;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(d);
            }
        }

        public void QuerySingleConfig(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                
                var result = new BaseRepository<CsvConfig>().FindSingle(u => u.CustomerCode==customercode&&u.TemplateCode==templatecode);
                
               
                context.Response.Write(JsonConvert.SerializeObject(result));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(d);
            }
        }


        public void Add(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                string customercode = context.Request.Params["CustomerCode"];
                string templatecode = context.Request.Params["TemplateCode"];
                //string headline = context.Request.Params["HeadLine"];
                //string contentline = context.Request.Params["ContentLine"];
                //string qtystartline = context.Request.Params["QtyStartLine"];
                var config = new BaseRepository<CsvConfig>().FindSingle(u => u.CustomerCode == customercode&&u.TemplateCode==templatecode);
                if (config == null)
                {
                    
                    CsvConfig c = new CsvConfig();
                    //string date = "20240618";
                    c.CustomerCode = customercode;
                    c.TemplateCode = templatecode;
                    //c.HeadLine = int.Parse(headline);
                    //c.ContentLine = int.Parse(contentline);
                    //c.QtyStartLine = int.Parse(qtystartline);

                    new BaseRepository<CsvConfig>().Add(c);
                    d.result = "success";
                    d.msg = "添加成功";
                }
                else
                {
                    d.result = "fail";
                    d.msg = "已存在该客户编号！";
                }

                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void Update(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                //var file = context.Request.Files[0];
                //if (file != null && (file.FileName.EndsWith(".xls") || file.FileName.EndsWith(".xlsx")))
                //{
                // 保存上传的文件到服务器临时目录
                //string filePath = Path.Combine("E:\\document\\test\\", Path.GetFileName(file.FileName));
                //file.SaveAs(filePath);

                // 读取 Excel 文件内容
                //Workbook workbook = new Workbook(filePath);
                //workbook.Save("E:\\document\\test\\test.csv", SaveFormat.Csv);

                //var fieldMappings = new BaseRepository<CsvField>().Find(null).ToList();
                //var fieldMappings = new List<FieldMapping>() {
                //new FieldMapping(){
                //    Id = 1,
                //    CsvHeader = "Code",
                //    DatabaseField = "CustPartNumber"
                //},
                //    new FieldMapping(){
                //        Id = 2,
                //        CsvHeader = "Description",
                //        DatabaseField = "BomDesc"
                //    },
                //    new FieldMapping(){
                //        Id = 3,
                //        CsvHeader = "Designator",
                //        DatabaseField = "RefBom"
                //    },
                //    new FieldMapping(){
                //        Id = 4,
                //        CsvHeader = "U/M",
                //        DatabaseField = "UomBom"
                //    },
                //    new FieldMapping(){
                //        Id = 5,
                //        CsvHeader = "MANUFACTURER",
                //        DatabaseField = "Manufacturer"
                //    },
                //    new FieldMapping(){
                //        Id = 6,
                //        CsvHeader = "MANUFACTURER P/N",
                //        DatabaseField = "ManPartNumber"
                //    },
                //    new FieldMapping(){
                //        Id = 7,
                //        CsvHeader = "REMARK",
                //        DatabaseField = "Notes"
                //    }
                //};

                //var records = CsvFile.Read<BomInfo>("E:\\document\\test\\test.csv", fieldMappings, 6,8);

                // 返回读取的内容
                //context.Response.Write("{\"success\":true, \"message\":\"文件上传成功\"}");


                // 删除临时文件
                //File.Delete(filePath);
                //}
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                //var headline = context.Request.Params["HeadLine"];
                //var contentline = context.Request.Params["ContentLine"];
                //var qtystartline = context.Request.Params["QtyStartLine"];
                new BaseRepository<CsvConfig>().Update(u => u.CustomerCode ==customercode&&u.TemplateCode==templatecode , u => new CsvConfig
                {
                    //Model=model,
                    //ModelDesc=modeldesc,
                    //HeadLine = int.Parse(headline),
                    //ContentLine = int.Parse(contentline),
                    //QtyStartLine=int.Parse(qtystartline),
                });

                d.result = "success";
                d.msg = "修改成功";
                context.Response.Write(JsonConvert.SerializeObject(d));

                
            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void BatchDelete(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                string nos = context.Request["nos"];
                if (string.IsNullOrEmpty(nos))
                {
                    d.result = "error";
                    d.msg = "客户编号为空重新选择";
                    context.Response.Write(JsonConvert.SerializeObject(d));
                    return;
                }

                string[] Array = nos.Split(',');
                foreach (string ar in Array)
                {
                    var customercode = ar.Split('-')[0];
                    var templatecode = ar.Split('-')[1];
                    if(templatecode!="")
                        new BaseRepository<CsvConfig>().Delete(u => u.CustomerCode == customercode && u.TemplateCode == templatecode);
                    else
                        new BaseRepository<CsvConfig>().Delete(u => u.CustomerCode == customercode);

                }
                d.result = "success";
                d.msg = "删除成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "error";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void QueryRule(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                var result = new BaseRepository<CsvField>().Find(c=>c.CustomerCode==customercode&&c.TemplateCode==templatecode);
                d.count = result.Count();
                //result = result.OrderBy(u => u.CsvHeader).Skip((10) * (int.Parse(context.Request["page"]) - 1)).Take((10));
                d.code = 0;
                d.msg = "";
                d.data = result.ToList();
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                d.count = 0;
                context.Response.Write(d);
            }
        }

        public void QueryAllRule(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                var result = new BaseRepository<CsvField>().Find(c => c.CustomerCode == customercode && c.TemplateCode == templatecode);
                d.code = 0;
                d.msg = "";
                d.data = result.ToList();
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                d.data = null;
                context.Response.Write(d);
            }
        }

        public void UploadHeader(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var file = context.Request.Files[0];
                User user = (User)HttpContext.Current.Session["UserInfo"];
                if (file != null)
                {
                    //保存上传的文件到服务器临时目录
                    string no = context.Request.QueryString["RFQNo"] ?? (string)HttpContext.Current.Session["RFQNo"];
                    //string no = "RFQ-IBS-20240910-001";
                    string filePath = @"\\172.20.0.14\System Static\RFQSystem\BOM_" + no+Path.GetExtension(file.FileName);
                    string name = Path.GetFileNameWithoutExtension(file.FileName);
                    //string no = HttpContext.Current.Session["RFQNo"].ToString();
                    
                    string customercode = context.Request.Params["CustomerCode"];
                    //var existbom = new BaseRepository<BomInfo>().Find(c => c.RFQNO == no);
                    
                    
                    file.SaveAs(filePath);

                    // 读取 Excel 文件内容
                    using (var excelPack = new ExcelPackage())
                    {
                        //Load excel stream
                        using (var stream = File.OpenRead(filePath))
                        {
                            excelPack.Load(stream);
                        }
                        var ws = excelPack.Workbook.Worksheets[0];
                        for (int i=1;i<=ws.Dimension.Columns; i++)
                        {
                            CsvField c = new CsvField();
                            if (!String.IsNullOrEmpty(ws.Cells[1, i].Text))
                            {
                                c.CsvHeader = ws.Cells[1, i].Text.Replace("\n", "").Replace(" ", "").Replace("\t", "").Replace("\r", "").ToUpper();
                                c.CustomerCode = customercode.Split('-')[0];
                                c.TemplateCode = customercode.Split('-')[1];
                                c.CreateTime = DateTime.Now;
                                c.SortId = i;
                                var existfield = new BaseRepository<CsvField>().FindSingle(u => u.CustomerCode == c.CustomerCode && u.TemplateCode == c.TemplateCode && u.CsvHeader == c.CsvHeader);
                                //删掉已有的重复映射
                                if (existfield != null)
                                {
                                    new BaseRepository<CsvField>().Delete(u=>u.Id==existfield.Id);
                                }
                                new BaseRepository<CsvField>().Add(c);
                            }
                        }
                        
                    }


                    // 删除临时文件
                    File.Delete(filePath);
                }


                d.result = "success";
                d.msg = "上传成功";
                context.Response.Write(JsonConvert.SerializeObject(d));


            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = "读取模板失败!请检查文件是否正确" + ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void UpdateField(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                
                
                var id = context.Request.Params["id"];
                var field = context.Request.Params["field"];
                new BaseRepository<CsvField>().Update(u => u.Id == id, u => new CsvField
                {
                    DatabaseField=field,
                });

                d.code = 0;
                d.msg = "修改成功";
                context.Response.Write(JsonConvert.SerializeObject(d));


            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void SaveModelMapping(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                string no = context.Request.QueryString["RFQNo"] ?? (string)HttpContext.Current.Session["RFQNo"];
                //string no = "RFQ-IBS-20240910-001";
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                RFQDBContext _context = new RFQDBContext();
                // 步骤1: 执行复杂查询
                var csvFields = _context.CsvFields
                    .Where(csf => _context.TypeInfos
                        .Where(rti => rti.RFQ_NO == no)
                        .Select(rti => rti.MODEL)
                        .Contains(csf.DatabaseField))
                     .Where(csf => csf.CustomerCode == customercode && csf.TemplateCode == templatecode)
                    .OrderBy(csf => csf.SortId)
                    .ToList();

                // 步骤2: 遍历结果并更新RFQ_TYPE_INFO
                int qpaCounter = 1;
                foreach (var csvField in csvFields)
                {
                    var typeInfo = _context.TypeInfos
                        .FirstOrDefault(ti => ti.MODEL == csvField.DatabaseField && ti.RFQ_NO == no);

                    if (typeInfo != null)
                    {
                        typeInfo.BOMQTYFIELD = $"QPA{qpaCounter}";
                        qpaCounter++;
                    }
                }

                // 步骤3: 保存更改
                _context.SaveChanges();

                d.code = 0;
                d.msg = "保存成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.code = 1;
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

            //public static  BomInfo ParseBom(string[] fields)
            //{
            //    BomInfo bom = new BomInfo();

            //        foreach(var field in fields)
            //    {
            //        if (field.Replace(" ", "") == "Code")
            //            bom.CustPartNumber = field;
            //        if (field.Replace(" ", "") == "Description")
            //            bom.BomDesc = field;
            //        if (field.Replace(" ", "") == "Designator")
            //            bom.RefBom = field;
            //        if (field.Replace(" ", "") == "U/M")
            //            bom.UomBom = field;
            //        if (field.Replace(" ", "") == "MANUFACTURER")
            //            bom.Manufacturer = field;
            //        if (field.Replace(" ", "") == "MANUFACTURERP/N")
            //            bom.ManPartNumber = field;
            //        if (field.Replace(" ", "") == "REMARK")
            //            bom.Notes = field;
            //    };


            //    return bom;
            //}

            public void AddRule(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                string excelfield = context.Request.Params["excelColumn"].Replace(" ","").ToUpper();
                string databasefield = context.Request.Params["databaseColumn"].Replace(" ", "").ToUpper();
                var csvfield = new BaseRepository<CsvField>().FindSingle(u => u.CustomerCode == customercode&&u.TemplateCode==templatecode&&u.DatabaseField== databasefield);
                if (csvfield == null)
                {

                    CsvField c = new CsvField();
                    //string date = "20240618";
                    c.CustomerCode = customercode;
                    c.TemplateCode = templatecode;
                    c.CsvHeader = excelfield;
                    c.DatabaseField = databasefield;

                    new BaseRepository<CsvField>().Add(c);
                    d.result = "success";
                    d.msg = "添加成功";
                }
                else
                {
                    d.result = "fail";
                    d.msg = "已存在该规则列！";
                }

                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "fail";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }

        public void DeleteRule(HttpContext context)
        {
            dynamic d = new ExpandoObject();
            try
            {
                string nos = context.Request["nos"];
                var code = context.Request.Params["CustomerCode"];
                var customercode = code.Split('-')[0];
                var templatecode = code.Split('-')[1];
                if (string.IsNullOrEmpty(nos))
                {
                    d.result = "error";
                    d.msg = "Excel列为空重新选择";
                    context.Response.Write(JsonConvert.SerializeObject(d));
                    return;
                }
                string[] Array = nos.Split(',');
                foreach (string ar in Array)
                {
                    new BaseRepository<CsvField>().Delete(u => u.Id==ar);
                }
                d.result = "success";
                d.msg = "删除成功";
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
            catch (Exception ex)
            {
                d.result = "error";
                d.msg = ex.Message;
                context.Response.Write(JsonConvert.SerializeObject(d));
            }
        }




    }
}