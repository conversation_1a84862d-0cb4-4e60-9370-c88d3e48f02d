/*!
 * @Title: select-input2.x
 * @Version: 1.0.0
 * @Description：Layui select配合input实现可输入，可选择，可搜索，支持异步加载，远程搜索，也可以本地data直接赋值，主要使用场景是select框可以自己输入，就是在下拉列表里找不到自己想要的选项就可以自己输入，同时还要支持模糊匹配功能，数据源可以从本地赋值，也可以异步url请求加载，或者直接远程请求联想
 * @Site: https://gitee.com/JerryZst/select-input2.x
 * @Author: jerryZst
 * @License：MIT
 */(()=>{var e={669:(e,t,n)=>{e.exports=n(609)},448:(e,t,n)=>{"use strict";var r=n(867),o=n(26),i=n(372),s=n(327),a=n(97),u=n(109),l=n(985),c=n(61),p=n(655),f=n(263);e.exports=function(e){return new Promise((function(t,n){var d,h=e.data,m=e.headers,v=e.responseType;function _(){e.cancelToken&&e.cancelToken.unsubscribe(d),e.signal&&e.signal.removeEventListener("abort",d)}r.isFormData(h)&&delete m["Content-Type"];var y=new XMLHttpRequest;if(e.auth){var g=e.auth.username||"",b=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";m.Authorization="Basic "+btoa(g+":"+b)}var w=a(e.baseURL,e.url);function x(){if(y){var r="getAllResponseHeaders"in y?u(y.getAllResponseHeaders()):null,i={data:v&&"text"!==v&&"json"!==v?y.response:y.responseText,status:y.status,statusText:y.statusText,headers:r,config:e,request:y};o((function(e){t(e),_()}),(function(e){n(e),_()}),i),y=null}}if(y.open(e.method.toUpperCase(),s(w,e.params,e.paramsSerializer),!0),y.timeout=e.timeout,"onloadend"in y?y.onloadend=x:y.onreadystatechange=function(){y&&4===y.readyState&&(0!==y.status||y.responseURL&&0===y.responseURL.indexOf("file:"))&&setTimeout(x)},y.onabort=function(){y&&(n(c("Request aborted",e,"ECONNABORTED",y)),y=null)},y.onerror=function(){n(c("Network Error",e,null,y)),y=null},y.ontimeout=function(){var t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",r=e.transitional||p.transitional;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(c(t,e,r.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",y)),y=null},r.isStandardBrowserEnv()){var k=(e.withCredentials||l(w))&&e.xsrfCookieName?i.read(e.xsrfCookieName):void 0;k&&(m[e.xsrfHeaderName]=k)}"setRequestHeader"in y&&r.forEach(m,(function(e,t){void 0===h&&"content-type"===t.toLowerCase()?delete m[t]:y.setRequestHeader(t,e)})),r.isUndefined(e.withCredentials)||(y.withCredentials=!!e.withCredentials),v&&"json"!==v&&(y.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&y.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&y.upload&&y.upload.addEventListener("progress",e.onUploadProgress),(e.cancelToken||e.signal)&&(d=function(e){y&&(n(!e||e&&e.type?new f("canceled"):e),y.abort(),y=null)},e.cancelToken&&e.cancelToken.subscribe(d),e.signal&&(e.signal.aborted?d():e.signal.addEventListener("abort",d))),h||(h=null),y.send(h)}))}},609:(e,t,n)=>{"use strict";var r=n(867),o=n(849),i=n(321),s=n(185);var a=function e(t){var n=new i(t),a=o(i.prototype.request,n);return r.extend(a,i.prototype,n),r.extend(a,n),a.create=function(n){return e(s(t,n))},a}(n(655));a.Axios=i,a.Cancel=n(263),a.CancelToken=n(972),a.isCancel=n(502),a.VERSION=n(288).version,a.all=function(e){return Promise.all(e)},a.spread=n(713),a.isAxiosError=n(268),e.exports=a,e.exports.default=a},263:e=>{"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},972:(e,t,n)=>{"use strict";var r=n(263);function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;this.promise.then((function(e){if(n._listeners){var t,r=n._listeners.length;for(t=0;t<r;t++)n._listeners[t](e);n._listeners=null}})),this.promise.then=function(e){var t,r=new Promise((function(e){n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.prototype.subscribe=function(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]},o.prototype.unsubscribe=function(e){if(this._listeners){var t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},502:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},321:(e,t,n)=>{"use strict";var r=n(867),o=n(327),i=n(782),s=n(572),a=n(185),u=n(875),l=u.validators;function c(e){this.defaults=e,this.interceptors={request:new i,response:new i}}c.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=a(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&u.assertOptions(t,{silentJSONParsing:l.transitional(l.boolean),forcedJSONParsing:l.transitional(l.boolean),clarifyTimeoutError:l.transitional(l.boolean)},!1);var n=[],r=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(r=r&&t.synchronous,n.unshift(t.fulfilled,t.rejected))}));var o,i=[];if(this.interceptors.response.forEach((function(e){i.push(e.fulfilled,e.rejected)})),!r){var c=[s,void 0];for(Array.prototype.unshift.apply(c,n),c=c.concat(i),o=Promise.resolve(e);c.length;)o=o.then(c.shift(),c.shift());return o}for(var p=e;n.length;){var f=n.shift(),d=n.shift();try{p=f(p)}catch(e){d(e);break}}try{o=s(p)}catch(e){return Promise.reject(e)}for(;i.length;)o=o.then(i.shift(),i.shift());return o},c.prototype.getUri=function(e){return e=a(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},r.forEach(["delete","get","head","options"],(function(e){c.prototype[e]=function(t,n){return this.request(a(n||{},{method:e,url:t,data:(n||{}).data}))}})),r.forEach(["post","put","patch"],(function(e){c.prototype[e]=function(t,n,r){return this.request(a(r||{},{method:e,url:t,data:n}))}})),e.exports=c},782:(e,t,n)=>{"use strict";var r=n(867);function o(){this.handlers=[]}o.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},97:(e,t,n)=>{"use strict";var r=n(793),o=n(303);e.exports=function(e,t){return e&&!r(t)?o(e,t):t}},61:(e,t,n)=>{"use strict";var r=n(481);e.exports=function(e,t,n,o,i){var s=new Error(e);return r(s,t,n,o,i)}},572:(e,t,n)=>{"use strict";var r=n(867),o=n(527),i=n(502),s=n(655),a=n(263);function u(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new a("canceled")}e.exports=function(e){return u(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||s.adapter)(e).then((function(t){return u(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(u(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},481:e=>{"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},e}},185:(e,t,n)=>{"use strict";var r=n(867);e.exports=function(e,t){t=t||{};var n={};function o(e,t){return r.isPlainObject(e)&&r.isPlainObject(t)?r.merge(e,t):r.isPlainObject(t)?r.merge({},t):r.isArray(t)?t.slice():t}function i(n){return r.isUndefined(t[n])?r.isUndefined(e[n])?void 0:o(void 0,e[n]):o(e[n],t[n])}function s(e){if(!r.isUndefined(t[e]))return o(void 0,t[e])}function a(n){return r.isUndefined(t[n])?r.isUndefined(e[n])?void 0:o(void 0,e[n]):o(void 0,t[n])}function u(n){return n in t?o(e[n],t[n]):n in e?o(void 0,e[n]):void 0}var l={url:s,method:s,data:s,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:u};return r.forEach(Object.keys(e).concat(Object.keys(t)),(function(e){var t=l[e]||i,o=t(e);r.isUndefined(o)&&t!==u||(n[e]=o)})),n}},26:(e,t,n)=>{"use strict";var r=n(61);e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},527:(e,t,n)=>{"use strict";var r=n(867),o=n(655);e.exports=function(e,t,n){var i=this||o;return r.forEach(n,(function(n){e=n.call(i,e,t)})),e}},655:(e,t,n)=>{"use strict";var r=n(867),o=n(16),i=n(481),s={"Content-Type":"application/x-www-form-urlencoded"};function a(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var u,l={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(u=n(448)),u),transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(a(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)||t&&"application/json"===t["Content-Type"]?(a(t,"application/json"),function(e,t,n){if(r.isString(e))try{return(t||JSON.parse)(e),r.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional||l.transitional,n=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,s=!n&&"json"===this.responseType;if(s||o&&r.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(s){if("SyntaxError"===e.name)throw i(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){l.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){l.headers[e]=r.merge(s)})),e.exports=l},288:e=>{e.exports={version:"0.24.0"}},849:e=>{"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},327:(e,t,n)=>{"use strict";var r=n(867);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var s=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(o(t)+"="+o(e))})))})),i=s.join("&")}if(i){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},303:e=>{"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},372:(e,t,n)=>{"use strict";var r=n(867);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,o,i,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(o)&&a.push("path="+o),r.isString(i)&&a.push("domain="+i),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},793:e=>{"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},268:e=>{"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},985:(e,t,n)=>{"use strict";var r=n(867);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=o(window.location.href),function(t){var n=r.isString(t)?o(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},16:(e,t,n)=>{"use strict";var r=n(867);e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},109:(e,t,n)=>{"use strict";var r=n(867),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,i,s={};return e?(r.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),n=r.trim(e.substr(i+1)),t){if(s[t]&&o.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}})),s):s}},713:e=>{"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},875:(e,t,n)=>{"use strict";var r=n(288).version,o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var i={};o.transitional=function(e,t,n){function o(e,t){return"[Axios v"+r+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,r,s){if(!1===e)throw new Error(o(r," has been removed"+(t?" in "+t:"")));return t&&!i[r]&&(i[r]=!0,console.warn(o(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,s)}},e.exports={assertOptions:function(e,t,n){if("object"!=typeof e)throw new TypeError("options must be an object");for(var r=Object.keys(e),o=r.length;o-- >0;){var i=r[o],s=t[i];if(s){var a=e[i],u=void 0===a||s(a,i,e);if(!0!==u)throw new TypeError("option "+i+" must be "+u)}else if(!0!==n)throw Error("Unknown option "+i)}},validators:o}},867:(e,t,n)=>{"use strict";var r=n(849),o=Object.prototype.toString;function i(e){return"[object Array]"===o.call(e)}function s(e){return void 0===e}function a(e){return null!==e&&"object"==typeof e}function u(e){if("[object Object]"!==o.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function l(e){return"[object Function]"===o.call(e)}function c(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),i(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:i,isArrayBuffer:function(e){return"[object ArrayBuffer]"===o.call(e)},isBuffer:function(e){return null!==e&&!s(e)&&null!==e.constructor&&!s(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isPlainObject:u,isUndefined:s,isDate:function(e){return"[object Date]"===o.call(e)},isFile:function(e){return"[object File]"===o.call(e)},isBlob:function(e){return"[object Blob]"===o.call(e)},isFunction:l,isStream:function(e){return a(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:c,merge:function e(){var t={};function n(n,r){u(t[r])&&u(n)?t[r]=e(t[r],n):u(n)?t[r]=e({},n):i(n)?t[r]=n.slice():t[r]=n}for(var r=0,o=arguments.length;r<o;r++)c(arguments[r],n);return t},extend:function(e,t,n){return c(t,(function(t,o){e[o]=n&&"function"==typeof t?r(t,n):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},73:(e,t,n)=>{"use strict";const r=JSON.parse('{"u2":"select-input2.x","i8":"1.0.0","v":"jerryZst"}');function o(e){return e.nodeType?e:document.querySelector(e)}function i(){for(var e=[],t=0;t<arguments.length;t++)e.push("".concat(t+1,". ").concat(arguments[t]));console.warn(e.join("\n"))}function s(e){return"[object Array]"===Object.prototype.toString.call(e)}function a(e){return"[object Function]"===Object.prototype.toString.call(e)}function u(e,t){var n;for(n in t)e[n]=e[n]&&"[object Object]"===e[n].toString()&&t[n]&&"[object Object]"===t[n].toString()?u(e[n],t[n]):e[n]=t[n];return e}var l,c,p,f,d,h,m={},v=[],_=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function y(e,t){for(var n in t)e[n]=t[n];return e}function g(e){var t=e.parentNode;t&&t.removeChild(e)}function b(e,t,n){var r,o,i,s={};for(i in t)"key"==i?r=t[i]:"ref"==i?o=t[i]:s[i]=t[i];if(arguments.length>2&&(s.children=arguments.length>3?l.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===s[i]&&(s[i]=e.defaultProps[i]);return w(e,s,r,o,null)}function w(e,t,n,r,o){var i={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++p:o};return null==o&&null!=c.vnode&&c.vnode(i),i}function x(e){return e.children}function k(e,t){this.props=e,this.context=t}function S(e,t){if(null==t)return e.__?S(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?S(e):null}function C(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return C(e)}}function E(e){(!e.__d&&(e.__d=!0)&&f.push(e)&&!O.__r++||h!==c.debounceRendering)&&((h=c.debounceRendering)||d)(O)}function O(){for(var e;O.__r=f.length;)e=f.sort((function(e,t){return e.__v.__b-t.__v.__b})),f=[],e.some((function(e){var t,n,r,o,i,s;e.__d&&(i=(o=(t=e).__v).__e,(s=t.__P)&&(n=[],(r=y({},o)).__v=o.__v+1,U(s,o,r,t.__n,void 0!==s.ownerSVGElement,null!=o.__h?[i]:null,n,null==i?S(o):i,o.__h),L(n,o),o.__e!=i&&C(o)))}))}function j(e,t,n,r,o,i,s,a,u,l){var c,p,f,d,h,_,y,g=r&&r.__k||v,b=g.length;for(n.__k=[],c=0;c<t.length;c++)if(null!=(d=n.__k[c]=null==(d=t[c])||"boolean"==typeof d?null:"string"==typeof d||"number"==typeof d||"bigint"==typeof d?w(null,d,null,null,d):Array.isArray(d)?w(x,{children:d},null,null,null):d.__b>0?w(d.type,d.props,d.key,null,d.__v):d)){if(d.__=n,d.__b=n.__b+1,null===(f=g[c])||f&&d.key==f.key&&d.type===f.type)g[c]=void 0;else for(p=0;p<b;p++){if((f=g[p])&&d.key==f.key&&d.type===f.type){g[p]=void 0;break}f=null}U(e,d,f=f||m,o,i,s,a,u,l),h=d.__e,(p=d.ref)&&f.ref!=p&&(y||(y=[]),f.ref&&y.push(f.ref,null,d),y.push(p,d.__c||h,d)),null!=h?(null==_&&(_=h),"function"==typeof d.type&&d.__k===f.__k?d.__d=u=P(d,u,e):u=T(e,d,f,g,h,u),"function"==typeof n.type&&(n.__d=u)):u&&f.__e==u&&u.parentNode!=e&&(u=S(f))}for(n.__e=_,c=b;c--;)null!=g[c]&&("function"==typeof n.type&&null!=g[c].__e&&g[c].__e==n.__d&&(n.__d=S(r,c+1)),B(g[c],g[c]));if(y)for(c=0;c<y.length;c++)M(y[c],y[++c],y[++c])}function P(e,t,n){for(var r,o=e.__k,i=0;o&&i<o.length;i++)(r=o[i])&&(r.__=e,t="function"==typeof r.type?P(r,t,n):T(n,r,r,o,r.__e,t));return t}function T(e,t,n,r,o,i){var s,a,u;if(void 0!==t.__d)s=t.__d,t.__d=void 0;else if(null==n||o!=i||null==o.parentNode)e:if(null==i||i.parentNode!==e)e.appendChild(o),s=null;else{for(a=i,u=0;(a=a.nextSibling)&&u<r.length;u+=2)if(a==o)break e;e.insertBefore(o,i),s=i}return void 0!==s?s:o.nextSibling}function N(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||_.test(t)?n:n+"px"}function R(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||N(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||N(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r||e.addEventListener(t,i?A:D,i):e.removeEventListener(t,i?A:D,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if("href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null!=n&&(!1!==n||"a"===t[0]&&"r"===t[1])?e.setAttribute(t,n):e.removeAttribute(t))}}function D(e){this.l[e.type+!1](c.event?c.event(e):e)}function A(e){this.l[e.type+!0](c.event?c.event(e):e)}function U(e,t,n,r,o,i,s,a,u){var l,p,f,d,h,m,v,_,g,b,w,S=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(u=n.__h,a=t.__e=n.__e,t.__h=null,i=[a]),(l=c.__b)&&l(t);try{e:if("function"==typeof S){if(_=t.props,g=(l=S.contextType)&&r[l.__c],b=l?g?g.props.value:l.__:r,n.__c?v=(p=t.__c=n.__c).__=p.__E:("prototype"in S&&S.prototype.render?t.__c=p=new S(_,b):(t.__c=p=new k(_,b),p.constructor=S,p.render=F),g&&g.sub(p),p.props=_,p.state||(p.state={}),p.context=b,p.__n=r,f=p.__d=!0,p.__h=[]),null==p.__s&&(p.__s=p.state),null!=S.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=y({},p.__s)),y(p.__s,S.getDerivedStateFromProps(_,p.__s))),d=p.props,h=p.state,f)null==S.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(null==S.getDerivedStateFromProps&&_!==d&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(_,b),!p.__e&&null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(_,p.__s,b)||t.__v===n.__v){p.props=_,p.state=p.__s,t.__v!==n.__v&&(p.__d=!1),p.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach((function(e){e&&(e.__=t)})),p.__h.length&&s.push(p);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(_,p.__s,b),null!=p.componentDidUpdate&&p.__h.push((function(){p.componentDidUpdate(d,h,m)}))}p.context=b,p.props=_,p.state=p.__s,(l=c.__r)&&l(t),p.__d=!1,p.__v=t,p.__P=e,l=p.render(p.props,p.state,p.context),p.state=p.__s,null!=p.getChildContext&&(r=y(y({},r),p.getChildContext())),f||null==p.getSnapshotBeforeUpdate||(m=p.getSnapshotBeforeUpdate(d,h)),w=null!=l&&l.type===x&&null==l.key?l.props.children:l,j(e,Array.isArray(w)?w:[w],t,n,r,o,i,s,a,u),p.base=t.__e,t.__h=null,p.__h.length&&s.push(p),v&&(p.__E=p.__=null),p.__e=!1}else null==i&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=I(n.__e,t,n,r,o,i,s,u);(l=c.diffed)&&l(t)}catch(e){t.__v=null,(u||null!=i)&&(t.__e=a,t.__h=!!u,i[i.indexOf(a)]=null),c.__e(e,t,n)}}function L(e,t){c.__c&&c.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){c.__e(e,t.__v)}}))}function I(e,t,n,r,o,i,s,a){var u,c,p,f=n.props,d=t.props,h=t.type,v=0;if("svg"===h&&(o=!0),null!=i)for(;v<i.length;v++)if((u=i[v])&&"setAttribute"in u==!!h&&(h?u.localName===h:3===u.nodeType)){e=u,i[v]=null;break}if(null==e){if(null===h)return document.createTextNode(d);e=o?document.createElementNS("http://www.w3.org/2000/svg",h):document.createElement(h,d.is&&d),i=null,a=!1}if(null===h)f===d||a&&e.data===d||(e.data=d);else{if(i=i&&l.call(e.childNodes),c=(f=n.props||m).dangerouslySetInnerHTML,p=d.dangerouslySetInnerHTML,!a){if(null!=i)for(f={},v=0;v<e.attributes.length;v++)f[e.attributes[v].name]=e.attributes[v].value;(p||c)&&(p&&(c&&p.__html==c.__html||p.__html===e.innerHTML)||(e.innerHTML=p&&p.__html||""))}if(function(e,t,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in t||R(e,i,null,n[i],r);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===t[i]||R(e,i,t[i],n[i],r)}(e,d,f,o,a),p)t.__k=[];else if(v=t.props.children,j(e,Array.isArray(v)?v:[v],t,n,r,o&&"foreignObject"!==h,i,s,i?i[0]:n.__k&&S(n,0),a),null!=i)for(v=i.length;v--;)null!=i[v]&&g(i[v]);a||("value"in d&&void 0!==(v=d.value)&&(v!==f.value||v!==e.value||"progress"===h&&!v)&&R(e,"value",v,f.value,!1),"checked"in d&&void 0!==(v=d.checked)&&v!==e.checked&&R(e,"checked",v,f.checked,!1))}return e}function M(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){c.__e(e,n)}}function B(e,t,n){var r,o;if(c.unmount&&c.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||M(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){c.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(o=0;o<r.length;o++)r[o]&&B(r[o],t,"function"!=typeof e.type);n||null==e.__e||g(e.__e),e.__e=e.__d=void 0}function F(e,t,n){return this.constructor(e,n)}function V(e,t,n){var r,o,i;c.__&&c.__(e,t),o=(r="function"==typeof n)?null:n&&n.__k||t.__k,i=[],U(t,e=(!r&&n||t).__k=b(x,null,[e]),o||m,m,void 0!==t.ownerSVGElement,!r&&n?[n]:o?null:t.firstChild?l.call(t.childNodes):null,i,!r&&n?n:o?o.__e:t.firstChild,r),L(i,e)}l=v.slice,c={__e:function(e,t){for(var n,r,o;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&null!=r.getDerivedStateFromError&&(n.setState(r.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),o=n.__d),o)return n.__E=n}catch(t){e=t}throw e}},p=0,k.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=y({},this.state),"function"==typeof e&&(e=e(y({},n),this.props)),e&&y(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),E(this))},k.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),E(this))},k.prototype.render=x,f=[],d="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,O.__r=0;var q=n(669),z=n.n(q),H=n(379),J=n.n(H),W=n(795),$=n.n(W),K=n(569),Z=n.n(K),X=n(565),G=n.n(X),Q=n(216),Y=n.n(Q),ee=n(589),te=n.n(ee),ne=n(21),re={};re.styleTagTransform=te(),re.setAttributes=G(),re.insert=Z().bind(null,"head"),re.domAPI=$(),re.insertStyleElement=Y();J()(ne.Z,re);ne.Z&&ne.Z.locals&&ne.Z.locals;function oe(e){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(e)}function ie(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function se(e,t){return se=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},se(e,t)}function ae(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=ce(e);if(t){var o=ce(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return ue(this,n)}}function ue(e,t){if(t&&("object"===oe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return le(e)}function le(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ce(e){return ce=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},ce(e)}var pe=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&se(e,t)}(i,e);var t,n,r,o=ae(i);function i(e){var t,n,r,s;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),t=o.call(this,e),n=le(t),s=void 0,(r="_searchInputRef")in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s,Ce[e.elem]=le(t),t.setState({value:t.props.initValue||"",showName:t.props.initValue||"",_value:t.props.initValue||"",remote:!0,disposable:!0,loading:!1,show:t.props.hasInitShow||!1,page:1,totalCount:0,data:[],itemClick:!1}),t.pageCount=0,t.copyData=t.props.data||[],t.searchCb=0,t.inputSearchOver=!0,t._value=t.props.initValue||"",t.inputSearchValue=!1,t}return t=i,n=[{key:"handleComposition",value:function(e){var t=e.type;"compositionstart"===t?(this.inputSearchOver=!1,this.searchCb&&clearTimeout(this.searchCb)):"compositionend"===t?(this.inputSearchOver=!0,this._inputChange(e)):this._inputChange(e)}},{key:"handleClickOutside",value:function(e){this._selfComponent.contains(e.target)||ke[this.props.elem].closed()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("click",this.handleClickOutside.bind(this))}},{key:"componentDidMount",value:function(){var e=this.base.querySelector(".select-input-input");e&&(e.addEventListener("compositionstart",this.handleComposition.bind(this)),e.addEventListener("compositionupdate",this.handleComposition.bind(this)),e.addEventListener("compositionend",this.handleComposition.bind(this)),e.addEventListener("input",this._inputChange.bind(this)),this.props.inputClickShow&&e.addEventListener("click",this._iconShow.bind(this))),document.addEventListener("click",this.handleClickOutside.bind(this)),this.props.done&&this.props.done()}},{key:"componentDidUpdate",value:function(){if(this.callback){this.callback=!1;var e=this.props.done();a(e)&&e(this.state.value,this.copyData||[])}}},{key:"componentWillReceiveProps",value:function(e){console.log(e)}},{key:"focus",value:function(e){this._searchInputRef&&this._searchInputRef.focus(),this.props.onFocus&&a(this.props.onFocus)&&this.props.onFocus(this._value,e)}},{key:"blur",value:function(e){this._searchInputRef&&this._searchInputRef.blur(),this.props.onBlur&&a(this.props.onBlur)&&this.props.onBlur(this._value,e)}},{key:"_inputChange",value:function(e){var t=this,n=e.target.value;if(!this.props.hasCut&&"insertFromPaste"===e.inputType)return!1;this.setState({show:!1}),this.searchCb&&clearTimeout(this.searchCb),this.setState({showName:n}),this.inputSearchValue=!0,this.inputSearchOver&&(this._value=n,this.searchCb=setTimeout((function(){t.callback=!0,t.setState({value:t._value,show:!0,itemClick:!1,remote:!0,page:1}),a(t.props.onInput)&&t.props.onInput(t._value,e)}),this.props.delay))}},{key:"_inputKeyDown",value:function(e){13===parseInt(e.keyCode)&&"keydown"===this.props.remoteEvent&&(this.props.pageRemote||this.props.remoteSearch)&&this.remoteData(this.state.page,!0)}},{key:"_optionClick",value:function(e){var t=this;this.props.clickClose&&this._iconShow(),this.inputSearchValue=!1,this.setState({value:e[this.props.prop[this.props.showProp]],showName:e[this.props.prop[this.props.showProp]],_value:e[this.props.prop.value],itemClick:!0},(function(){t.props.onClick&&a(t.props.onClick)&&t.props.onClick(e),t.props.paging&&(t._changePage(1),t.props.pageRemote&&t.remoteData(1,!0))}))}},{key:"_iconShow",value:function(){var e=this;this.setState({show:!this.state.show},(function(){var t=e.state.show?"show":"hide",n=e.props[t]||null;n&&a(n)&&n(),e.state.show&&e.focus()}))}},{key:"_changePage",value:function(e){this.setState({page:e})}},{key:"_prevPage",value:function(){arguments.length>0&&void 0!==arguments[0]||this.pageCount;var e=this.state.page;if(e<=1)return!1;e-=1,this._changePage(e),this.props.pageRemote&&this.remoteData(e,!0)}},{key:"_nextPage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.pageCount,t=this.state.page;t>=e||(t+=1,this._changePage(t),this.props.pageRemote&&this.remoteData(t,!0))}},{key:"remoteData",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.state.page,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(this.state.remote||n)&&(this.callback=!1,this.setState({loading:!0,remote:!1}),this.blur(),this.props.remoteMethod(this.state.value,(function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;setTimeout((function(){a(e.props.parseData)&&(t=e.props.parseData(t)),e.focus(),e.callback=!0,e.copyData=t,e.setState({data:t,loading:!1,totalCount:n})}),10)}),t))}},{key:"getData",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"get",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.state.disposable&&(this.setState({disposable:!1}),z()[n](e,"get"===n?{params:r}:r).then((function(e){var n=e.data;if(n[t.props.statusCode]===t.props.statusOK){var r=n[t.props.statusData]||[];a(t.props.parseData)&&(r=t.props.parseData(r)),t.setState({data:r}),t.copyData=r}})).catch((function(e){console.log(e)})))}},{key:"getValue",value:function(){if(this.props.invisibleMode){var e=this.state._value;this.inputSearchValue&&(e=this._value);for(var t={name:"",value:e,isSelect:!1},n=this.state.data||[],r=0;r<n.length;r++){if(e===n[r].name){t.name=n[r].name,t.value=n[r].value,t.isSelect=!0;break}if(e===n[r].value){t.name=n[r].name,t.isSelect=!0;break}}return t}return this.state._value}},{key:"render",value:function(e,t){var n=this,r=e.hasSelectIcon,o=e.localSearch,i=e.pageRemote,s=e.remoteSearch,a=e.filterMethod,u=e.prop,l=e.isPureSelectMode,c=e.ignoreCase;l&&(r=!0);var p=this.state.data;e.hasData&&this.state.disposable&&(this.setState({disposable:!1,data:e.data}),this.copyData=e.data),e.url&&e.data.length<=0&&!s&&!i&&this.getData(e.url,e.method,e.params),(i||s)&&this.remoteData(),!o||s||i||this.state.itemClick||this.state.value===e.initValue||(p=p.filter((function(e,t){return a(n.state.value,e,t,u,c)})));var f="";if(e.paging){var d=e.pageRemote?Math.ceil(this.state.totalCount/e.pageSize):Math.floor((p.length-1)/e.pageSize)+1;d<=0&&(d=1);var h=this.state.page;if(h>d&&(h=d),d>0&&h<=0&&(h=1),!e.pageRemote){var m=(h-1)*e.pageSize,v=m+e.pageSize;p=p.slice(m,v)}var _="select-input-no-drop",y={},g={};h<=1&&(y=_),h===d&&(g=_),this.state.page!==h&&this._changePage(h),this.pageCount=d,f=b("div",{className:"select-input-paging"},b("span",{className:y,onClick:this._prevPage.bind(this,d)},"上一页"),b("span",null,this.state.page," / ",d),b("span",{className:g,onClick:this._nextPage.bind(this,d)},"下一页"))}var w=p.length<=0?"dis":this.state.show?"":"dis";w="select-input-body "+w,p=p.map((function(t){return b("li",{className:"select-input-option-content",onClick:n._optionClick.bind(n,t),"data-value":t[e.prop.value]},t[e.prop.name])}));var x="";r&&(x=b("i",{className:"select-input-icon "+(this.state.show?"select-input-icon-expand":""),onClick:this._iconShow.bind(this)}));var k="";this.state.loading&&(k=b("div",{className:"select-input-loading"},b("span",{class:"select-input-loader"})));var S="";return e.height&&(S+="max-height: "+e.height),b("div",{className:"select-input"},b("div",{className:"select-input-content",ref:function(e){return n._selfComponent=e}},b("div",{className:"select-input-container"},b("input",{className:"select-input-input",name:e.name,autoComplete:"off",readOnly:l,value:this.state.showName,placeholder:e.placeholder,"lay-filter":e.layFilter,"lay-verify":e.layVerify,"lay-vertype":e.layVerType,"lay-reqtext":e.layReqText,ref:function(e){return n._searchInputRef=e},onKeyDown:this._inputKeyDown.bind(this),onBlur:this.blur.bind(this),onFocus:this.focus.bind(this)}),x),b("div",{className:w},b("div",{className:"scroll-body",style:S},b("ul",{className:"select-input-option"},p),k),f)))}}],n&&ie(t.prototype,n),r&&ie(t,r),i}(k);const fe=pe;function de(){return de=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(this,arguments)}function he(e){return he="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},he(e)}function me(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}var ve=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.init(t)}var t,n,r;return t=e,n=[{key:"init",value:function(e){this.options={elem:"",url:"",method:"get",params:{},statusCode:"code",statusOK:0,statusData:"data",statusMessage:"msg",data:[],initValue:"",name:"select",height:"200px",layFilter:"",layVerify:"",layVerType:"",layReqText:"",hasSelectIcon:!1,invisibleMode:!1,hasInitShow:!1,hasCut:!0,ignoreCase:!1,placeholder:"请输入",uniqueId:"",delay:200,remoteEvent:"",filterMethod:function(e,t,n,r,o){if(!e)return!0;var i=t[r.name],s=e;return o&&(i=i.toLowerCase(),s=s.toLowerCase()),-1!=i.indexOf(s)},localSearch:!0,remoteSearch:!1,remoteMethod:function(e,t){t([])},paging:!1,pageSize:10,pageRemote:!1,clickClose:!0,inputClickShow:!0,showProp:"name",isPureSelectMode:!1,prop:{name:"name",value:"value"},show:function(){},hide:function(){},parseData:function(e){return e},on:function(e){e.arr,e.item,e.selected},onInput:function(e){},onBlur:function(e){},onFocus:function(e){},onClick:function(e){},done:function(){},error:function(){}},this.renderData(e)}},{key:"renderData",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=!!e.data;this.options=u(this.options,e),this.options.render_success=!1;var n=this.options.dom;if(n){var r=this.options.data||[];if("function"==typeof r&&(r=r(),this.options.data=r),s(r))return V(b(fe,de({},this.options,{_update:Date.now(),hasData:t,onReset:this.onReset.bind(this)})),n),this.options.render_success=!0,this;i("data数据必须为数组类型, 不能是".concat(he(r),"类型"))}else i("没有找到渲染对象: ".concat(e.elem,", 请检查"))}},{key:"refresh",value:function(){Ce[this.options.elem].forceUpdate()}},{key:"onReset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.options.elem;e=u(Se[t],e),this.init(e)}},{key:"getValue",value:function(){return Ce[this.options.elem].getValue()}},{key:"getText",value:function(){return Ce[this.options.elem]._value}},{key:"getSelect",value:function(){var e=Ce[this.options.elem],t=e.state.data||[],n=e.state._value;e.inputSearchValue&&(n=e._value);for(var r={name:"",value:n,isSelect:!1},o=0;o<t.length;o++){if(n===t[o].name){r.name=t[o].name,r.value=t[o].value,r.isSelect=!0;break}if(n===t[o].value){r.name=t[o].name,r.isSelect=!0;break}}return r}},{key:"setValue",value:function(e){for(var t=Ce[this.options.elem],n=t.state.data||[],r=e,o=!1,i=0;i<n.length;i++)if(e===n[i].value){r=n[i].name,o=!0;break}return t.inputSearchValue=!o,t.inputSearchValue||(t._value=r),t.setState({value:r,_value:r,showName:r,itemClick:!1}),this}},{key:"emptyValue",value:function(){var e=Ce[this.options.elem];return e.setState({value:"",_value:"",showName:"",itemClick:!1},(function(){(e.props.pageRemote||e.props.remoteSearch)&&(e._changePage(1),e.remoteData(1,!0))})),this}},{key:"append",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(s(e)){var n=Ce[this.options.elem];if(!n.props.pageRemote&&!n.props.remoteSearch){var r=n.state.data;return t&&(e=r.concat(e)),Ce[this.options.elem].setState({data:e}),this}i("远程分页和远程搜索不支持追加数据")}else i("请传入数组结构...")}},{key:"opened",value:function(){var e=Ce[this.options.elem];return!e.state.show&&e._iconShow(),this}},{key:"closed",value:function(){var e=Ce[this.options.elem];return e.state.show&&e._iconShow(),this}}],n&&me(t.prototype,n),r&&me(t,r),e}();const _e=ve;function ye(e){return function(e){if(Array.isArray(e))return ge(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ge(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ge(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ge(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var be=r.u2,we=r.i8,xe=r.v,ke={},Se={},Ce={};const Ee={name:be,version:we,author:xe,render:function(e){var t=e.elem;if(e.dom=o(t),t.nodeType){var n="SELECT_INPUT_RENDER_"+Date.now()+"_"+Math.random();t.setAttribute(be,n),t="[".concat(be,"='").concat(n,"']"),e.elem=t}Se[t]=e;var r=new _e(e);return r&&r.options.render_success&&(ke[t]=r),r},get:function(e,t){var n;switch(Object.prototype.toString.call(e)){case"[object String]":e&&(n=function(t){return t===e});break;case"[object RegExp]":n=function(t){return e.test(t)};break;case"[object Function]":n=e}var r=Object.keys(ke),i=(n?r.filter(n):r).map((function(e){return ke[e]})).filter((function(e){return o(e.options.elem)}));return t?i[0]:i},batch:function(e,t){var n=Array.prototype.slice.call(arguments);return n.splice(0,2),this.get(e).map((function(e){return e[t].apply(e,ye(n))}))}};e=n.hmd(e);var Oe="selectInput";"object"===("undefined"==typeof exports?"undefined":_typeof(exports))?e.exports=Ee:"function"==typeof define&&n.amdO?define(SelectInput):window.layui&&layui.define&&layui.define((function(e){e(Oe,Ee)})),window.selectInput=Ee},21:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(81),o=n.n(r),i=n(645),s=n.n(i)()(o());s.push([e.id,"@-webkit-keyframes select-input-upbit {\r\n    from {\r\n        -webkit-transform: translate3d(0, 30px, 0);\r\n        opacity: 0.3;\r\n    }\r\n    to {\r\n        -webkit-transform: translate3d(0, 0, 0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@keyframes select-input-upbit {\r\n    from {\r\n        transform: translate3d(0, 30px, 0);\r\n        opacity: 0.3;\r\n    }\r\n    to {\r\n        transform: translate3d(0, 0, 0);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n@-webkit-keyframes select-input-loader {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n@keyframes select-input-loader {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n        transform: rotate(0deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n        transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n\r\n.select-input {\r\n    padding: 0;\r\n    margin: 0;\r\n}\r\n\r\n.select-input .select-input-content {\r\n    position: relative;\r\n    width: 90%;\r\n    margin: 20px auto;\r\n}\r\n\r\n.select-input .select-input-container {\r\n    width: 100%;\r\n    height: 36px;\r\n    line-height: 36px;\r\n    position: relative;\r\n}\r\n\r\n.select-input .select-input-input {\r\n    outline: none;\r\n    width: 100%;\r\n    height: 100%;\r\n    line-height: 100%;\r\n    border: 1px solid #E6E6E6;\r\n    font-size: 14px;\r\n    border-radius: 2px;\r\n    cursor: pointer;\r\n    padding: 0 10px;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.select-input .select-input-input::-webkit-input-placeholder, .layui-select::-webkit-input-placeholder, .layui-textarea::-webkit-input-placeholder {\r\n    line-height: 1.3;\r\n    color: #e6e6e6;\r\n}\r\n\r\n.select-input-input:hover,\r\n.select-input-input:focus {\r\n    border: 1px solid #C0C4CC;\r\n}\r\n\r\n.select-input-icon {\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    position: absolute;\r\n    width: 0;\r\n    height: 0;\r\n    right: 10px;\r\n    top: 50%;\r\n    margin-top: -1px;\r\n    cursor: pointer;\r\n    border: 6px dashed transparent;\r\n    border-top-color: #C2C2C2;\r\n    border-top-style: solid;\r\n    transition: all 0.3s;\r\n    -webkit-transition: all 0.3s;\r\n}\r\n\r\n.select-input-icon-expand {\r\n    margin-top: -8px;\r\n    transform: rotate(180deg);\r\n}\r\n\r\n.select-input-body {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 42px;\r\n    padding: 5px 0;\r\n    z-index: 999;\r\n    width: 100%;\r\n    min-width: fit-content;\r\n    box-sizing: border-box;\r\n    border: 1px solid #E6E6E6;\r\n    background-color: #fff;\r\n    border-radius: 2px;\r\n    box-shadow: 0 2px 4px rgb(0 0 0 / 12%);\r\n    animation-name: select-input-upbit;\r\n    animation-duration: 0.3s;\r\n    animation-fill-mode: both;\r\n}\r\n\r\n.select-input .scroll-body {\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n    max-height: 200px;\r\n}\r\n\r\n.select-input .scroll-body::-webkit-scrollbar {\r\n    /*滚动条整体样式*/\r\n    width: 6px;\r\n    height: 6px;\r\n}\r\n\r\n.select-input .scroll-body::-webkit-scrollbar-thumb {\r\n    /*滚动条里面小方块样式*/\r\n    border-radius: 100px;\r\n    background: #999;\r\n}\r\n\r\n.select-input .scroll-body::-webkit-scrollbar-track {\r\n    /*滚动条里面轨道样式*/\r\n    /* -webkit-box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2); */\r\n    border-radius: 0;\r\n    background: #fff;\r\n}\r\n\r\n.select-input .select-input-option {\r\n    align-items: center;\r\n    position: relative;\r\n    cursor: pointer;\r\n}\r\n\r\n.select-input .select-input-option-content {\r\n    position: relative;\r\n    padding-left: 15px;\r\n    height: 36px;\r\n    line-height: 36px;\r\n    overflow: hidden;\r\n    white-space: nowrap;\r\n    text-overflow: ellipsis;\r\n    color: #666;\r\n    font-size: 14px;\r\n}\r\n\r\n.select-input .select-input-option-content:hover {\r\n    background-color: rgb(242, 242, 242)\r\n}\r\n\r\n.select-input .select-input-paging {\r\n    padding: 0 10px;\r\n    display: flex;\r\n    margin-top: 5px;\r\n}\r\n\r\n.select-input .select-input-paging > span {\r\n    height: 30px;\r\n    line-height: 30px;\r\n    display: flex;\r\n    flex: 1;\r\n    justify-content: center;\r\n    vertical-align: middle;\r\n    margin: 0 -1px 0 0;\r\n    background-color: #fff;\r\n    color: #333;\r\n    font-size: 12px;\r\n    border: 1px solid #e2e2e2;\r\n    flex-wrap: nowrap;\r\n    width: 100%;\r\n    overflow: hidden;\r\n    min-width: 50px;\r\n    cursor: pointer;\r\n}\r\n\r\n.select-input .select-input-paging > span:first-child {\r\n    border-radius: 2px 0 0 2px;\r\n}\r\n\r\n.select-input .select-input-paging > span:last-child {\r\n    border-radius: 0 2px 2px 0;\r\n}\r\n\r\n.select-input .select-input-paging > span.select-input-no-drop {\r\n    cursor: no-drop;\r\n    color: rgb(210, 210, 210);\r\n}\r\n\r\n.select-input .select-input-label {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0px;\r\n    right: 30px;\r\n    box-sizing: border-box;\r\n    overflow: auto hidden;\r\n}\r\n\r\n.select-input .scroll {\r\n    overflow-y: hidden;\r\n}\r\n\r\n.select-input .label-content {\r\n    line-height: 30px;\r\n    display: flex;\r\n    padding: 3px 10px;\r\n    flex-wrap: nowrap;\r\n    white-space: nowrap;\r\n    position: relative;\r\n}\r\n\r\n.select-input .dis {\r\n    display: none;\r\n}\r\n\r\n.select-input .show {\r\n    display: block;\r\n}\r\n\r\n.select-input .select-input-label-block {\r\n    height: 26px;\r\n    line-height: 26px;\r\n    background-color: rgb(0, 150, 136);\r\n    display: flex;\r\n    position: relative;\r\n    padding: 0px 24px 0 5px;\r\n    margin: 3px 5px 3px 0;\r\n    border-radius: 3px;\r\n    align-items: baseline;\r\n    color: #FFF;\r\n    font-size: 14px;\r\n}\r\n\r\n.select-input .select-input-icon-close {\r\n    position: absolute;\r\n    top: 6px;\r\n    right: 5px;\r\n    width: 14px;\r\n    height: 14px;\r\n    overflow: hidden;\r\n    cursor: pointer;\r\n}\r\n\r\n.select-input .select-input-icon-close::before,\r\n.select-input .select-input-icon-close::after {\r\n    content: '';\r\n    position: absolute;\r\n    height: 2px;\r\n    width: 100%;\r\n    top: 50%;\r\n    left: 0;\r\n    margin-top: -1px;\r\n    background: #fff;\r\n}\r\n\r\n.select-input .select-input-icon-close::before {\r\n    -webkit-transform: rotate(45deg);\r\n    -moz-transform: rotate(45deg);\r\n    -ms-transform: rotate(45deg);\r\n    -o-transform: rotate(45deg);\r\n    transform: rotate(45deg);\r\n}\r\n\r\n.select-input .select-input-icon-close::after {\r\n    -webkit-transform: rotate(-45deg);\r\n    -moz-transform: rotate(-45deg);\r\n    -ms-transform: rotate(-45deg);\r\n    -o-transform: rotate(-45deg);\r\n    transform: rotate(-45deg);\r\n}\r\n\r\n.select-input .select-input-icon-close.rounded::before,\r\n.select-input .select-input-icon-close.rounded::after {\r\n    border-radius: 5px;\r\n}\r\n\r\n/*loading*/\r\n\r\n.select-input .select-input-loading {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(255, 255, 255, .6);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.select-input .select-input-loader {\r\n    border: .2em dotted currentcolor;\r\n    border-radius: 50%;\r\n    -webkit-animation: 1s loader linear infinite;\r\n    animation: 1s select-input-loader linear infinite;\r\n    display: inline-block;\r\n    width: 1em;\r\n    height: 1em;\r\n    color: inherit;\r\n    vertical-align: middle;\r\n    pointer-events: none;\r\n}\r\n\r\n.select-input .mask {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgb(255, 255, 255);\r\n}\r\n\r\n.select-input .loadEffect {\r\n    position: fixed;\r\n    left: 50%;\r\n    top: 50%;\r\n    margin-top: -20px;\r\n    margin-left: -20px;\r\n    width: 40px;\r\n    height: 40px;\r\n}\r\n\r\n.select-input .loadEffect div {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    -webkit-animation: load 1.48s linear infinite;\r\n}\r\n\r\n.select-input .loadEffect div span {\r\n    display: inline-block;\r\n    width: 10px;\r\n    height: 10px;\r\n    border-radius: 50%;\r\n    background: rgb(0, 150, 136);\r\n    position: absolute;\r\n    left: 50%;\r\n    margin-top: -10px;\r\n    margin-left: -10px;\r\n}\r\n\r\n@-webkit-keyframes load {\r\n    0% {\r\n        -webkit-transform: rotate(0deg);\r\n    }\r\n    10% {\r\n        -webkit-transform: rotate(45deg);\r\n    }\r\n    50% {\r\n        opacity: 1;\r\n        -webkit-transform: rotate(160deg);\r\n    }\r\n    62% {\r\n        opacity: 0;\r\n    }\r\n    65% {\r\n        opacity: 0;\r\n        -webkit-transform: rotate(200deg);\r\n    }\r\n    90% {\r\n        -webkit-transform: rotate(340deg);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg);\r\n    }\r\n}\r\n\r\n.select-input .loadEffect div:nth-child(1) {\r\n    -webkit-animation-delay: 0.2s;\r\n}\r\n\r\n.select-input .loadEffect div:nth-child(2) {\r\n    -webkit-animation-delay: 0.4s;\r\n}\r\n\r\n.select-input .loadEffect div:nth-child(3) {\r\n    -webkit-animation-delay: 0.6s;\r\n}\r\n\r\n.select-input .loadEffect div:nth-child(4) {\r\n    -webkit-animation-delay: 0.8s;\r\n}\r\n",""]);const a=s},645:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(r)for(var a=0;a<this.length;a++){var u=this[a][0];null!=u&&(s[u]=!0)}for(var l=0;l<e.length;l++){var c=[].concat(e[l]);r&&s[c[0]]||(void 0!==i&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=i),n&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=n):c[2]=n),o&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=o):c[4]="".concat(o)),t.push(c))}},t}},81:e=>{"use strict";e.exports=function(e){return e[1]}},379:e=>{"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},s=[],a=0;a<e.length;a++){var u=e[a],l=r.base?u[0]+r.base:u[0],c=i[l]||0,p="".concat(l," ").concat(c);i[l]=c+1;var f=n(p),d={css:u[1],media:u[2],sourceMap:u[3],supports:u[4],layer:u[5]};if(-1!==f)t[f].references++,t[f].updater(d);else{var h=o(d,r);r.byIndex=a,t.splice(a,0,{identifier:p,updater:h,references:1})}s.push(p)}return s}function o(e,t){var n=t.domAPI(t);n.update(e);return function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var i=r(e=e||[],o=o||{});return function(e){e=e||[];for(var s=0;s<i.length;s++){var a=n(i[s]);t[a].references--}for(var u=r(e,o),l=0;l<i.length;l++){var c=n(i[l]);0===t[c].references&&(t[c].updater(),t.splice(c,1))}i=u}}},569:e=>{"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},216:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},565:(e,t,n)=>{"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},795:e=>{"use strict";e.exports=function(e){var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},589:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={id:r,loaded:!1,exports:{}};return e[r](i,i.exports,n),i.loaded=!0,i.exports}n.amdO={},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);n(73)})();