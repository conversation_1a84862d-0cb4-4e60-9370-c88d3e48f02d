<!DOCTYPE html>
<html>
<head>
    <% for (var chunk in htmlWebpackPlugin.files.css) { %>
        <link rel="preload" href="<%= htmlWebpackPlugin.files.css[chunk] %>" as="style">
    <% } %>
    <% for (var chunk in htmlWebpackPlugin.files.chunks) { %>
        <link rel="preload" href="<%= htmlWebpackPlugin.files.chunks[chunk].entry %>" as="script">
    <% } %>
    <meta charset="utf-8">
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>
    <title>select-input</title>
    <link rel="stylesheet" href="https://www.layuicdn.com/layui-v2.5.5/css/layui.css">
    <script src="https://www.layuicdn.com/layui-v2.5.5/layui.all.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
<div id="appContiner">
    <div id="app">

    </div>
</div>
<button type="button" class="layui-btn" onclick="window.btn()">获取值</button>
<button type="button" class="layui-btn" onclick="window.btn1()">设置值</button>
<button type="button" class="layui-btn" onclick="window.btn2()">清空dom强制重新渲染</button>
</body>
<script>
    layui.use(['jquery'], function () {
        var $ = layui.jquery;
        var __ins = window.selectInput.render({
            elem: "#app",
            //         url: 'https://shop.douaili.com/api/v1/user/test',
            statusOK: 200,
            name: "name",
            initValue: '苹果',
            localSearch: false,
            data: [
                {name: 'BBB', value: 1},
                {name: 'AAA', value: 2},
                {name: 'CCC', value: 3},
            ],
            hasSelectIcon: true,
            hasInitShow: true,
            paging: true,
            remoteSearch: false,
            pageRemote: false,
            ignoreCase: true,
            //         remoteMethod: function (keyword, cb, page) {
            //             axios.get('https://shop.douaili.com/api/v1/user/test', {
            //                 params: {
            //                     key: keyword,
            //                     page: page
            //                 }
            //             }).then(function (response) {
            //                 // handle success
            //                 let ret = response.data;
            //                 return cb(ret.data, 10)
            //             }).catch(function (error) {
            //                 // handle error
            //                 console.log(error);
            //             });
            //         },
            pageSize: 2,
            invisibleMode: true,
            onFocus(item) {
                console.log(item)
            },
            onBlur(item) {
                console.log(item)
            },
            onClick(item) {
                console.log(item)
            },
            done() {
                //console.log('我渲染完成了哦')
            }
        })

        window.btn = function () {
            console.log(__ins.getValue())
        }

        window.btn1 = function () {
            __ins.setValue(1)
        }

        window.btn2 = function () {
            $("#app").empty();
            $("#app").html('<div id="app"></div>')
            __ins.refresh();
        }
    })
</script>
</html>
