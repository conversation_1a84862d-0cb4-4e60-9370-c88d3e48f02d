# 下载模板问题修复说明

## 问题描述
本地调试时提示："下载失败：无法使用前导 .. 在顶级目录上退出"

## 问题原因
在`DownloadTemplate`方法中使用了`context.Server.MapPath("~/../" + templateFileName)`，这个路径试图从网站根目录向上退出，在某些安全配置下是不被允许的。

## 修复内容

### 1. 移除有问题的路径
删除了`context.Server.MapPath("~/../" + templateFileName)`这个会导致错误的路径。

### 2. 优化路径查找逻辑
现在按以下优先级查找文件：
1. **网站根目录**: `~/批量上传.xlsx`
2. **网络共享路径**: `\\***********\System Static\RFQSystem\批量上传.xlsx`
3. **应用程序目录**: `[BaseDirectory]\批量上传.xlsx`
4. **应用程序上级目录**: `[BaseDirectory]\..\批量上传.xlsx`

### 3. 添加调试功能
- 添加了`TestPaths`方法来查看所有路径信息
- 在下载失败时显示详细的路径查找过程

## 测试步骤

### 1. 路径测试
访问：`http://localhost/ashx/ProjectController.ashx?action=TestPaths`
这会显示所有可能的文件路径和文件是否存在。

### 2. 下载测试
访问：`http://localhost/ashx/ProjectController.ashx?action=DownloadTemplate`
如果文件存在，应该开始下载；如果不存在，会显示详细的调试信息。

### 3. 使用测试页面
打开`test-download.html`，点击相应的测试链接。

## 文件部署建议

### 方案1：放在网站根目录（推荐）
将`批量上传.xlsx`文件复制到项目的根目录，与`Default.aspx`同级。

### 方案2：放在应用程序目录
将文件放在`bin`目录的同级目录。

### 方案3：使用网络共享
确保网络路径`\\***********\System Static\RFQSystem\`可访问，并将文件放在该目录。

## 常见问题解决

### 问题1：路径权限错误
**解决方案**：
- 确保IIS应用程序池有读取文件的权限
- 给文件添加`IIS_IUSRS`读取权限

### 问题2：文件不存在
**解决方案**：
- 使用`TestPaths`功能查看所有路径
- 将文件复制到显示的任一路径中

### 问题3：中文文件名问题
**解决方案**：
- 确保文件名完全匹配，包括中文字符
- 检查文件编码是否正确

## 验证清单

修复后请验证：
- [ ] `TestPaths`显示至少一个路径中文件存在
- [ ] 下载链接不再显示路径错误
- [ ] 能够成功下载文件
- [ ] 下载的文件可以正常打开

## 调试信息说明

### TestPaths输出示例：
```
路径测试结果：

当前工作目录: C:\Windows\System32\inetsrv
应用程序基目录: C:\inetpub\wwwroot\YourApp\bin\
网站根目录: C:\inetpub\wwwroot\YourApp\

路径 1: C:\inetpub\wwwroot\YourApp\批量上传.xlsx
文件存在: True

路径 2: \\***********\System Static\RFQSystem\批量上传.xlsx
文件存在: False

路径 3: C:\inetpub\wwwroot\YourApp\bin\批量上传.xlsx
文件存在: False

路径 4: C:\inetpub\wwwroot\YourApp\批量上传.xlsx
文件存在: True
```

### 下载失败时的调试信息：
如果所有路径都找不到文件，系统会显示详细的查找过程，帮助定位问题。

## 后续优化建议

1. **配置化路径**：将文件路径配置到web.config中
2. **缓存机制**：缓存文件路径避免重复查找
3. **日志记录**：记录下载操作到日志文件
4. **版本管理**：支持模板文件的版本管理
