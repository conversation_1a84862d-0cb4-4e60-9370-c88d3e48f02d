# 模板文件部署指南

## 问题描述
服务器上点击下载模板时提示"无法下载批量上传模板.xlsx，找不到文件"。

## 解决方案

### 1. 后端处理方式（推荐）
已修改为通过ashx处理文件下载，避免直接访问静态文件的权限问题。

#### 修改内容：
- **前端链接**：从 `href="../批量上传.xlsx"` 改为 `href="../ashx/ProjectController.ashx?action=DownloadTemplate"`
- **后端处理**：在ProjectController.ashx.cs中添加了DownloadTemplate方法

#### 文件查找路径优先级：
1. 项目根目录：`~/批量上传.xlsx`
2. 上级目录：`~/../批量上传.xlsx`
3. 网络共享路径：`\\172.20.0.14\System Static\RFQSystem\批量上传.xlsx`
4. 应用程序目录：`AppDomain.CurrentDomain.BaseDirectory\批量上传.xlsx`

### 2. 文件部署步骤

#### 方案A：部署到项目根目录（推荐）
1. 将`批量上传.xlsx`文件复制到服务器上的项目根目录
2. 确保IIS应用程序池有读取该文件的权限

#### 方案B：部署到网络共享路径
1. 将`批量上传.xlsx`文件复制到`\\172.20.0.14\System Static\RFQSystem\`目录
2. 确保IIS应用程序池有访问网络共享的权限

### 3. 权限配置

#### IIS权限设置：
1. 打开IIS管理器
2. 选择对应的应用程序池
3. 设置应用程序池标识为具有文件访问权限的账户
4. 或者给IIS_IUSRS用户组添加文件读取权限

#### 文件权限设置：
```
右键点击批量上传.xlsx文件 → 属性 → 安全 → 编辑
添加IIS_IUSRS用户组，给予读取权限
```

### 4. 测试步骤

1. **检查文件是否存在**：
   - 登录服务器，确认文件在预期位置
   - 检查文件名是否正确（注意中文字符）

2. **测试下载功能**：
   - 访问：`http://your-server/RFQSystem/ashx/ProjectController.ashx?action=DownloadTemplate`
   - 应该直接开始下载文件

3. **查看错误信息**：
   - 如果下载失败，页面会显示具体错误信息
   - 根据错误信息调整文件路径或权限

### 5. 常见问题排查

#### 问题1：文件不存在
**现象**：页面显示"模板文件不存在，请联系管理员"
**解决**：
- 确认文件已复制到服务器
- 检查文件路径是否正确
- 确认文件名完全匹配（包括中文字符）

#### 问题2：权限不足
**现象**：页面显示权限相关错误
**解决**：
- 给IIS应用程序池配置适当的权限
- 给文件添加IIS_IUSRS读取权限

#### 问题3：网络路径访问失败
**现象**：无法访问`\\172.20.0.14\System Static\RFQSystem\`
**解决**：
- 确认网络连接正常
- 配置IIS应用程序池使用域账户
- 或将文件复制到本地路径

### 6. 备用方案

如果ashx方式仍有问题，可以考虑：

1. **使用相对路径**：将文件放在wwwroot下的子目录
2. **配置IIS MIME类型**：确保.xlsx文件类型被正确识别
3. **使用虚拟目录**：在IIS中创建指向文件位置的虚拟目录

### 7. 验证清单

部署完成后，请验证：
- [ ] 文件已复制到服务器指定位置
- [ ] IIS权限配置正确
- [ ] 下载链接可以正常工作
- [ ] 下载的文件完整且可以打开
- [ ] 不同浏览器都能正常下载

## 技术说明

新的下载方式通过后端处理，具有以下优势：
1. **安全性**：避免直接暴露文件路径
2. **灵活性**：可以动态查找文件位置
3. **兼容性**：支持多种文件存储方案
4. **错误处理**：提供详细的错误信息
